/**
 * Comprehensive error boundary for special sets components
 * Provides graceful error handling with recovery options
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON>w, Bug, Home } from 'lucide-react';
import { specialSetEventBus, SPECIAL_SET_EVENTS } from '@/contexts/SpecialSetsContext';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  allowRetry?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  retryCount: number;
}

export class SpecialSetsErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      errorInfo
    });

    // Log error details
    console.error('SpecialSets Error Boundary caught an error:', error);
    console.error('Error Info:', errorInfo);

    // Emit error event for monitoring
    specialSetEventBus.emit('error:boundary', {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      errorInfo,
      errorId: this.state.errorId,
      retryCount: this.state.retryCount
    });

    // Call custom error handler
    this.props.onError?.(error, errorInfo);

    // Report to error monitoring service (if available)
    this.reportError(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // Integration point for error monitoring services like Sentry
    if (typeof window !== 'undefined' && (window as any).Sentry) {
      (window as any).Sentry.captureException(error, {
        contexts: {
          react: {
            componentStack: errorInfo.componentStack
          }
        },
        tags: {
          errorBoundary: 'SpecialSets',
          errorId: this.state.errorId
        }
      });
    }
  };

  private handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }));

      // Emit retry event
      specialSetEventBus.emit('error:retry', {
        errorId: this.state.errorId,
        retryCount: this.state.retryCount + 1
      });
    }
  };

  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0
    });

    // Clear any cached data that might be causing issues
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem('special-sets-store');
        sessionStorage.clear();
      } catch (e) {
        console.warn('Failed to clear storage:', e);
      }
    }

    // Emit reset event
    specialSetEventBus.emit('error:reset', {
      errorId: this.state.errorId
    });
  };

  private getErrorSeverity = (error: Error): 'low' | 'medium' | 'high' | 'critical' => {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'medium';
    }
    
    if (message.includes('chunk') || message.includes('loading')) {
      return 'high';
    }
    
    if (message.includes('memory') || message.includes('maximum call stack')) {
      return 'critical';
    }
    
    return 'low';
  };

  private renderErrorDetails = () => {
    if (!this.props.showDetails || !this.state.error) return null;

    return (
      <details className="mt-4 p-3 bg-gray-50 rounded border">
        <summary className="cursor-pointer font-medium text-sm text-gray-700 mb-2">
          Technical Details
        </summary>
        <div className="space-y-2 text-xs text-gray-600">
          <div>
            <strong>Error:</strong> {this.state.error.message}
          </div>
          <div>
            <strong>Error ID:</strong> {this.state.errorId}
          </div>
          <div>
            <strong>Retry Count:</strong> {this.state.retryCount}/{this.maxRetries}
          </div>
          {this.state.error.stack && (
            <div>
              <strong>Stack Trace:</strong>
              <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32">
                {this.state.error.stack}
              </pre>
            </div>
          )}
        </div>
      </details>
    );
  };

  private renderErrorUI = () => {
    const { error } = this.state;
    const { allowRetry = true } = this.props;
    
    if (!error) return null;

    const severity = this.getErrorSeverity(error);
    const canRetry = allowRetry && this.state.retryCount < this.maxRetries;

    const severityConfig = {
      low: {
        icon: Bug,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200'
      },
      medium: {
        icon: AlertTriangle,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200'
      },
      high: {
        icon: AlertTriangle,
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200'
      },
      critical: {
        icon: AlertTriangle,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200'
      }
    };

    const config = severityConfig[severity];
    const Icon = config.icon;

    return (
      <Card className={`${config.bgColor} ${config.borderColor} border-2`}>
        <CardHeader>
          <CardTitle className={`flex items-center gap-2 ${config.color}`}>
            <Icon className="h-5 w-5" />
            Special Sets Error
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-gray-700">
            <p className="font-medium mb-2">Something went wrong with the special sets feature.</p>
            <p className="text-sm text-gray-600">
              {severity === 'critical' 
                ? 'This is a critical error that requires immediate attention.'
                : severity === 'high'
                ? 'This error may affect the functionality of special sets.'
                : 'This is a minor error that should not affect your workout.'
              }
            </p>
          </div>

          <div className="flex flex-wrap gap-2">
            {canRetry && (
              <Button 
                onClick={this.handleRetry}
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
              >
                <RefreshCw className="h-4 w-4" />
                Try Again ({this.maxRetries - this.state.retryCount} left)
              </Button>
            )}
            
            <Button 
              onClick={this.handleReset}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <Home className="h-4 w-4" />
              Reset Special Sets
            </Button>
            
            <Button 
              onClick={() => window.location.reload()}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-4 w-4" />
              Reload Page
            </Button>
          </div>

          {this.renderErrorDetails()}
        </CardContent>
      </Card>
    );
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Render error UI
      return this.renderErrorUI();
    }

    return this.props.children;
  }
}

// Higher-order component for easy wrapping
export function withSpecialSetsErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <SpecialSetsErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </SpecialSetsErrorBoundary>
  );

  WrappedComponent.displayName = `withSpecialSetsErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Hook for error reporting from functional components
export function useErrorReporting() {
  const reportError = React.useCallback((error: Error, context?: Record<string, any>) => {
    console.error('Special Sets Error:', error);
    
    specialSetEventBus.emit('error:reported', {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      context,
      timestamp: Date.now()
    });

    // Report to monitoring service
    if (typeof window !== 'undefined' && (window as any).Sentry) {
      (window as any).Sentry.captureException(error, {
        extra: context,
        tags: {
          source: 'useErrorReporting'
        }
      });
    }
  }, []);

  return { reportError };
}
