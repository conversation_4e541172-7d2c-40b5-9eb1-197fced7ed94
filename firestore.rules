    rules_version = '2';
    service cloud.firestore {
      match /databases/{database}/documents {
        // WARNING: These rules are HIGHLY permissive and are ONLY for development/testing.
        // They allow any authenticated user to read/write to ANY document in 'chats' and 'workouts' collections.
        // DO NOT use these in a production environment without significant modification!

        match /workouts/{workoutId} {
          allow read: if request.auth != null &&
            (resource.data.userId == request.auth.uid || resource.data.isPublic);
          allow create: if request.auth != null &&
            request.resource.data.userId == request.auth.uid;
          allow update, delete: if request.auth != null &&
            resource.data.userId == request.auth.uid;
        }

        match /user_workout_stats/{userId} {
          allow read: if request.auth != null;
          allow write: if request.auth != null &&
            request.auth.uid == userId;
        }

        // Allow authenticated users to read and write their own user profiles
        match /users/{userId} {
          allow read, write: if request.auth != null && request.auth.uid == userId;
        }

        // Enhanced user profiles with social features - Fixed permissions
        match /user_profiles/{userId} {
          // Allow read if user is authenticated AND (profile is public OR user owns it)
          allow read: if request.auth != null &&
            (resource.data.isPublic == true || request.auth.uid == userId);

          // Allow create/update only by the owner with valid data
          allow create, update: if request.auth != null &&
            request.auth.uid == userId &&
            request.resource.data.uid == userId;

          // Allow delete only by the owner
          allow delete: if request.auth != null && request.auth.uid == userId;
        }

        // AI personality profiles - private to user (DEVELOPMENT - PERMISSIVE)
        match /ai_personality_profiles/{userId} {
          allow read, write: if request.auth != null;
        }

        // User context for AI - private to user
        match /user_context/{userId} {
          allow read, write: if request.auth != null && request.auth.uid == userId;
        }

        // Social following relationships - Fixed permissions
        match /user_following/{followId} {
          // Allow read if user is involved in the relationship
          allow read: if request.auth != null &&
            (request.auth.uid == resource.data.followerId ||
             request.auth.uid == resource.data.followingId);

          // Allow create if user is the follower and data is valid
          allow create: if request.auth != null &&
            request.auth.uid == request.resource.data.followerId &&
            request.resource.data.followerId != request.resource.data.followingId;

          // Allow delete if user is the follower (unfollowing)
          allow delete: if request.auth != null &&
            request.auth.uid == resource.data.followerId;
        }

        // Workout posts for social feed (DEVELOPMENT - PERMISSIVE)
        match /workout_posts/{postId} {
          allow read, write: if request.auth != null;
        }

        // Feed posts
        match /feed_posts/{postId} {
          allow read: if request.auth != null;
          allow create: if request.auth != null &&
            request.resource.data.userId == request.auth.uid;
          allow update, delete: if request.auth != null &&
            resource.data.userId == request.auth.uid;
        }

        // Workout interactions (likes, comments, etc.) (DEVELOPMENT - PERMISSIVE)
        match /workout_interactions/{interactionId} {
          allow read, write: if request.auth != null;
        }

        // AI recommendations - private to user
        match /ai_recommendations/{recommendationId} {
          allow read, write: if request.auth != null &&
            resource.data.userId == request.auth.uid;
        }

        // Allow authenticated users to read and write to any document in 'chats' collection. (For development/testing)
        match /chats/{document=**} {
          allow read, write: if request.auth != null;
        }

        // Chat sessions (DEVELOPMENT - PERMISSIVE)
        match /chat_sessions/{sessionId} {
          allow read, write: if request.auth != null;
        }

        // Chat messages (DEVELOPMENT - PERMISSIVE)
        match /chat_messages/{messageId} {
          allow read, write: if request.auth != null;
        }

        // Onboarding contexts - private to user
        match /onboarding_contexts/{userId} {
          allow read, write: if request.auth != null && request.auth.uid == userId;
        }

        // Profile pictures - private to user
        match /profile_pictures/{pictureId} {
          allow read, write: if request.auth != null &&
            resource.data.userId == request.auth.uid;
          allow create: if request.auth != null &&
            request.resource.data.userId == request.auth.uid;
        }

        // User settings - private to user
        match /user_settings/{userId} {
          allow read, write: if request.auth != null && request.auth.uid == userId;
        }

        // User sessions - private to user
        match /user_sessions/{sessionId} {
          allow read, write: if request.auth != null &&
            resource.data.userId == request.auth.uid;
          allow create: if request.auth != null &&
            request.resource.data.userId == request.auth.uid;
        }

        // Conversation states for agentic AI - private to user
        match /conversation_states/{sessionId} {
          allow read, write: if request.auth != null &&
            resource.data.userId == request.auth.uid;
          allow create: if request.auth != null &&
            request.resource.data.userId == request.auth.uid;
        }
      }
    }