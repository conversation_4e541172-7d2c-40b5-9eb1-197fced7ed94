/**
 * Performance tests for special sets system
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { exerciseDataManager } from '@/lib/data-flow/exercise-data-manager';
import { specialSetsCache } from '@/lib/cache/special-sets-cache';
import { ExerciseWithSets, SpecialSetResponse, SpecialSetTemplate } from '@/types/special-sets-unified';

describe('Special Sets Performance', () => {
  beforeEach(() => {
    // Clear cache and reset data manager
    specialSetsCache.clear();
    exerciseDataManager.destroy();
  });

  describe('Exercise Data Manager Performance', () => {
    it('should handle large exercise datasets efficiently', () => {
      const largeExerciseSet = Array.from({ length: 1000 }, (_, i) => ({
        id: `ex${i}`,
        name: `Exercise ${i}`,
        primaryMuscleGroup: `muscle${i % 10}`,
        sets: Array.from({ length: 5 }, (_, j) => ({
          id: `set${i}-${j}`,
          weight: 100 + j * 10,
          reps: 10 - j,
          isExecuted: false
        }))
      })) as ExerciseWithSets[];

      const start = performance.now();
      exerciseDataManager.setExercises(largeExerciseSet);
      const end = performance.now();

      expect(end - start).toBeLessThan(100); // Should complete within 100ms
      expect(exerciseDataManager.getExercises()).toHaveLength(1000);
    });

    it('should batch updates efficiently', async () => {
      const exercises = Array.from({ length: 100 }, (_, i) => ({
        id: `ex${i}`,
        name: `Exercise ${i}`,
        sets: [{ id: `set${i}`, weight: 100, reps: 10, isExecuted: false }]
      })) as ExerciseWithSets[];

      exerciseDataManager.setExercises(exercises);

      const start = performance.now();
      
      // Perform many updates
      for (let i = 0; i < 100; i++) {
        exerciseDataManager.updateExercise(`ex${i}`, 'sets.0.weight', 150 + i);
      }

      // Force flush to measure batching efficiency
      exerciseDataManager.flushUpdates();
      const end = performance.now();

      expect(end - start).toBeLessThan(50); // Batching should make this fast
    });

    it('should handle special set group operations efficiently', () => {
      const exercises = Array.from({ length: 50 }, (_, i) => ({
        id: `ex${i}`,
        name: `Exercise ${i}`,
        sets: [{ id: `set${i}`, weight: 100, reps: 10, isExecuted: false }]
      })) as ExerciseWithSets[];

      exerciseDataManager.setExercises(exercises);

      const start = performance.now();
      
      // Create multiple special set groups
      for (let i = 0; i < 10; i++) {
        const exerciseIds = [`ex${i * 2}`, `ex${i * 2 + 1}`];
        exerciseDataManager.createSpecialSetGroup(
          `group${i}`,
          'superset',
          {
            type: 'superset',
            rounds: 3,
            restBetweenExercises: 0,
            restBetweenSets: 90,
            exercises: exerciseIds
          },
          exerciseIds
        );
      }

      const end = performance.now();

      expect(end - start).toBeLessThan(100);
      expect(exerciseDataManager.getSpecialSetGroups().size).toBe(10);
    });

    it('should maintain performance with frequent state updates', () => {
      const exercises = Array.from({ length: 20 }, (_, i) => ({
        id: `ex${i}`,
        name: `Exercise ${i}`,
        sets: [{ id: `set${i}`, weight: 100, reps: 10, isExecuted: false }]
      })) as ExerciseWithSets[];

      exerciseDataManager.setExercises(exercises);

      const updateCount = 1000;
      const start = performance.now();

      for (let i = 0; i < updateCount; i++) {
        const exerciseIndex = i % 20;
        exerciseDataManager.updateExercise(`ex${exerciseIndex}`, 'sets.0.weight', 100 + i);
      }

      exerciseDataManager.flushUpdates();
      const end = performance.now();

      const timePerUpdate = (end - start) / updateCount;
      expect(timePerUpdate).toBeLessThan(0.1); // Less than 0.1ms per update
    });
  });

  describe('Cache Performance', () => {
    it('should provide fast cache lookups', () => {
      const testData = Array.from({ length: 100 }, (_, i) => ({
        id: `special-set-${i}`,
        userId: 'user1',
        workoutId: 'workout1',
        type: 'superset' as const,
        parameters: {
          type: 'superset' as const,
          rounds: 3,
          restBetweenExercises: 0,
          restBetweenSets: 90,
          exercises: [`ex${i}`, `ex${i + 1}`]
        },
        exerciseIds: [`ex${i}`, `ex${i + 1}`],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })) as SpecialSetResponse[];

      // Populate cache
      testData.forEach(item => {
        specialSetsCache.setSpecialSet(item);
      });

      const start = performance.now();
      
      // Perform many lookups
      for (let i = 0; i < 1000; i++) {
        const index = i % 100;
        specialSetsCache.getSpecialSet(`special-set-${index}`);
      }

      const end = performance.now();

      expect(end - start).toBeLessThan(10); // Should be very fast
    });

    it('should handle cache eviction efficiently', () => {
      // Set small cache size for testing
      specialSetsCache.setMaxSize(50);

      const start = performance.now();

      // Add more items than cache size
      for (let i = 0; i < 100; i++) {
        const item = {
          id: `special-set-${i}`,
          userId: 'user1',
          workoutId: 'workout1',
          type: 'superset' as const,
          parameters: {
            type: 'superset' as const,
            rounds: 3,
            restBetweenExercises: 0,
            restBetweenSets: 90,
            exercises: [`ex${i}`, `ex${i + 1}`]
          },
          exerciseIds: [`ex${i}`, `ex${i + 1}`],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        } as SpecialSetResponse;

        specialSetsCache.setSpecialSet(item);
      }

      const end = performance.now();

      expect(end - start).toBeLessThan(50);
      expect(specialSetsCache.getCacheSize()).toBeLessThanOrEqual(50);
    });

    it('should maintain good hit rates under load', () => {
      const templates = Array.from({ length: 20 }, (_, i) => ({
        id: `template-${i}`,
        name: `Template ${i}`,
        description: `Description ${i}`,
        type: 'superset' as const,
        parameters: {
          type: 'superset' as const,
          rounds: 3,
          restBetweenExercises: 0,
          restBetweenSets: 90,
          exercises: [`ex${i}`, `ex${i + 1}`]
        },
        difficulty: 'intermediate' as const,
        estimatedDuration: 600,
        targetMuscleGroups: ['chest', 'back'],
        createdBy: 'user1',
        rating: 4.5,
        usageCount: 10,
        tags: ['strength']
      })) as SpecialSetTemplate[];

      // Populate cache
      templates.forEach(template => {
        specialSetsCache.setTemplate(template);
      });

      // Simulate realistic access pattern (80/20 rule)
      const accessPattern = Array.from({ length: 1000 }, () => {
        return Math.random() < 0.8 
          ? Math.floor(Math.random() * 4) // 80% access to first 4 items
          : Math.floor(Math.random() * 20); // 20% access to all items
      });

      accessPattern.forEach(index => {
        specialSetsCache.getTemplate(`template-${index}`);
      });

      const hitRate = specialSetsCache.getHitRate();
      expect(hitRate).toBeGreaterThan(0.8); // Should have good hit rate
    });
  });

  describe('Memory Usage', () => {
    it('should not leak memory with frequent updates', () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;

      // Perform many operations
      for (let cycle = 0; cycle < 10; cycle++) {
        const exercises = Array.from({ length: 100 }, (_, i) => ({
          id: `ex${cycle}-${i}`,
          name: `Exercise ${i}`,
          sets: [{ id: `set${i}`, weight: 100, reps: 10, isExecuted: false }]
        })) as ExerciseWithSets[];

        exerciseDataManager.setExercises(exercises);

        // Create and destroy special sets
        for (let i = 0; i < 10; i++) {
          const groupId = `group${cycle}-${i}`;
          exerciseDataManager.createSpecialSetGroup(
            groupId,
            'superset',
            {
              type: 'superset',
              rounds: 3,
              restBetweenExercises: 0,
              restBetweenSets: 90,
              exercises: [`ex${cycle}-${i * 2}`, `ex${cycle}-${i * 2 + 1}`]
            },
            [`ex${cycle}-${i * 2}`, `ex${cycle}-${i * 2 + 1}`]
          );
        }

        // Clean up
        exerciseDataManager.setExercises([]);
      }

      // Force garbage collection if available
      if ((global as any).gc) {
        (global as any).gc();
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });

    it('should clean up event listeners properly', () => {
      const listeners: (() => void)[] = [];

      // Subscribe to data manager
      for (let i = 0; i < 100; i++) {
        const unsubscribe = exerciseDataManager.subscribe(() => {});
        listeners.push(unsubscribe);
      }

      // Unsubscribe all
      listeners.forEach(unsubscribe => unsubscribe());

      // Verify cleanup by checking that updates don't trigger callbacks
      let callbackCount = 0;
      const testUnsubscribe = exerciseDataManager.subscribe(() => {
        callbackCount++;
      });

      exerciseDataManager.setExercises([{
        id: 'test',
        name: 'Test',
        sets: [{ id: 'set1', weight: 100, reps: 10, isExecuted: false }]
      }]);

      expect(callbackCount).toBe(1); // Only the test callback should be called

      testUnsubscribe();
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle concurrent updates safely', async () => {
      const exercises = Array.from({ length: 10 }, (_, i) => ({
        id: `ex${i}`,
        name: `Exercise ${i}`,
        sets: [{ id: `set${i}`, weight: 100, reps: 10, isExecuted: false }]
      })) as ExerciseWithSets[];

      exerciseDataManager.setExercises(exercises);

      // Simulate concurrent updates
      const promises = Array.from({ length: 100 }, async (_, i) => {
        return new Promise<void>(resolve => {
          setTimeout(() => {
            exerciseDataManager.updateExercise(`ex${i % 10}`, 'sets.0.weight', 100 + i);
            resolve();
          }, Math.random() * 10);
        });
      });

      await Promise.all(promises);
      exerciseDataManager.flushUpdates();

      // Verify final state is consistent
      const finalExercises = exerciseDataManager.getExercises();
      expect(finalExercises).toHaveLength(10);
      
      // All exercises should have valid weights
      finalExercises.forEach(exercise => {
        expect(exercise.sets[0].weight).toBeGreaterThanOrEqual(100);
      });
    });

    it('should maintain cache consistency under concurrent access', async () => {
      const promises = Array.from({ length: 50 }, async (_, i) => {
        return new Promise<void>(resolve => {
          setTimeout(() => {
            const item = {
              id: `concurrent-${i}`,
              userId: 'user1',
              workoutId: 'workout1',
              type: 'superset' as const,
              parameters: {
                type: 'superset' as const,
                rounds: 3,
                restBetweenExercises: 0,
                restBetweenSets: 90,
                exercises: [`ex${i}`, `ex${i + 1}`]
              },
              exerciseIds: [`ex${i}`, `ex${i + 1}`],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            } as SpecialSetResponse;

            specialSetsCache.setSpecialSet(item);
            
            // Immediately try to retrieve
            const retrieved = specialSetsCache.getSpecialSet(`concurrent-${i}`);
            expect(retrieved).toBeTruthy();
            
            resolve();
          }, Math.random() * 20);
        });
      });

      await Promise.all(promises);

      // Verify all items are accessible
      for (let i = 0; i < 50; i++) {
        const item = specialSetsCache.getSpecialSet(`concurrent-${i}`);
        expect(item).toBeTruthy();
        expect(item?.id).toBe(`concurrent-${i}`);
      }
    });
  });
});
