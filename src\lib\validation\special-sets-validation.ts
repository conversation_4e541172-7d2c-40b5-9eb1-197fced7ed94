import { 
  SpecialSetType, 
  SpecialSetParameters, 
  ExerciseWithSets,
  SPECIAL_SET_VALIDATION_RULES 
} from '@/types/special-sets-unified';
import { ValidationMessage } from '@/components/ui/validation-feedback';

export interface SpecialSetValidationResult {
  isValid: boolean;
  messages: ValidationMessage[];
  canProceed: boolean; // True if only warnings, false if errors
}

export class SpecialSetValidator {
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }

  validateType(type: SpecialSetType): ValidationMessage[] {
    const messages: ValidationMessage[] = [];
    
    if (!type) {
      messages.push({
        id: this.generateId(),
        level: 'error',
        message: 'Special set type is required',
        field: 'type'
      });
    } else if (!SPECIAL_SET_VALIDATION_RULES[type]) {
      messages.push({
        id: this.generateId(),
        level: 'error',
        message: `Unknown special set type: ${type}`,
        field: 'type'
      });
    }

    return messages;
  }

  validateExerciseSelection(exercises: ExerciseWithSets[], type: SpecialSetType): ValidationMessage[] {
    const messages: ValidationMessage[] = [];
    const rules = SPECIAL_SET_VALIDATION_RULES[type];

    if (!rules) return messages;

    // Check exercise count
    if ('exercises' in rules) {
      const { min, max } = rules.exercises;
      
      if (exercises.length < min) {
        messages.push({
          id: this.generateId(),
          level: 'error',
          message: `${type} requires at least ${min} exercise${min !== 1 ? 's' : ''}`,
          field: 'exercises'
        });
      }
      
      if (exercises.length > max) {
        messages.push({
          id: this.generateId(),
          level: 'error',
          message: `${type} supports maximum ${max} exercise${max !== 1 ? 's' : ''}`,
          field: 'exercises'
        });
      }
    }

    // Check if exercises have sets
    const exercisesWithoutSets = exercises.filter(ex => !ex.sets || ex.sets.length === 0);
    if (exercisesWithoutSets.length > 0) {
      messages.push({
        id: this.generateId(),
        level: 'error',
        message: 'All exercises must have at least one set',
        field: 'exercises'
      });
    }

    // Check for incomplete sets
    const exercisesWithIncompleteSets = exercises.filter(ex => 
      ex.sets.some(set => !set.weight || !set.reps)
    );
    if (exercisesWithIncompleteSets.length > 0) {
      messages.push({
        id: this.generateId(),
        level: 'warning',
        message: 'Some exercises have incomplete set data (missing weight or reps)',
        field: 'exercises'
      });
    }

    // Type-specific validations
    if (type === 'dropset' && exercises.length > 1) {
      messages.push({
        id: this.generateId(),
        level: 'error',
        message: 'Drop sets can only be applied to a single exercise',
        field: 'exercises'
      });
    }

    if (type === 'restpause' && exercises.length > 1) {
      messages.push({
        id: this.generateId(),
        level: 'error',
        message: 'Rest-pause sets can only be applied to a single exercise',
        field: 'exercises'
      });
    }

    // Muscle group compatibility validation removed - users can use any muscle groups

    return messages;
  }

  validateParameters(parameters: SpecialSetParameters, type: SpecialSetType): ValidationMessage[] {
    const messages: ValidationMessage[] = [];
    const rules = SPECIAL_SET_VALIDATION_RULES[type];

    if (!rules) return messages;

    // Validate rounds
    if ('rounds' in rules && 'rounds' in parameters) {
      const { min, max } = rules.rounds;
      const rounds = parameters.rounds;
      
      if (rounds < min) {
        messages.push({
          id: this.generateId(),
          level: 'error',
          message: `Minimum ${min} round${min !== 1 ? 's' : ''} required`,
          field: 'rounds'
        });
      }
      
      if (rounds > max) {
        messages.push({
          id: this.generateId(),
          level: 'error',
          message: `Maximum ${max} round${max !== 1 ? 's' : ''} allowed`,
          field: 'rounds'
        });
      }

      // Warning for excessive rounds
      if (rounds > 5) {
        messages.push({
          id: this.generateId(),
          level: 'warning',
          message: 'High number of rounds may lead to excessive fatigue',
          field: 'rounds'
        });
      }
    }

    // Validate rest periods
    if ('restBetweenSets' in rules && 'restBetweenSets' in parameters) {
      const { min, max } = rules.restBetweenSets;
      const rest = parameters.restBetweenSets;
      
      if (rest < min) {
        messages.push({
          id: this.generateId(),
          level: 'error',
          message: `Minimum ${min} seconds rest required`,
          field: 'restBetweenSets'
        });
      }
      
      if (rest > max) {
        messages.push({
          id: this.generateId(),
          level: 'warning',
          message: `Rest period of ${rest} seconds is quite long`,
          field: 'restBetweenSets'
        });
      }
    }

    // Validate dropset-specific parameters
    if (type === 'dropset' && 'dropPercentage' in parameters) {
      const dropPercentage = parameters.dropPercentage;
      
      if (dropPercentage < 10) {
        messages.push({
          id: this.generateId(),
          level: 'warning',
          message: 'Drop percentage below 10% may not provide sufficient intensity reduction',
          field: 'dropPercentage'
        });
      }
      
      if (dropPercentage > 50) {
        messages.push({
          id: this.generateId(),
          level: 'warning',
          message: 'Drop percentage above 50% may be too aggressive',
          field: 'dropPercentage'
        });
      }
    }

    // Validate rest-pause specific parameters
    if (type === 'restpause' && 'restPauseDuration' in parameters) {
      const restPause = parameters.restPauseDuration;
      
      if (restPause < 10) {
        messages.push({
          id: this.generateId(),
          level: 'warning',
          message: 'Rest-pause duration below 10 seconds may not allow sufficient recovery',
          field: 'restPauseDuration'
        });
      }
      
      if (restPause > 30) {
        messages.push({
          id: this.generateId(),
          level: 'warning',
          message: 'Rest-pause duration above 30 seconds may reduce intensity',
          field: 'restPauseDuration'
        });
      }
    }

    return messages;
  }

  validateComplete(
    type: SpecialSetType,
    parameters: SpecialSetParameters,
    exercises: ExerciseWithSets[]
  ): SpecialSetValidationResult {
    const messages: ValidationMessage[] = [
      ...this.validateType(type),
      ...this.validateExerciseSelection(exercises, type),
      ...this.validateParameters(parameters, type)
    ];

    const hasErrors = messages.some(msg => msg.level === 'error');
    const hasWarnings = messages.some(msg => msg.level === 'warning');

    return {
      isValid: !hasErrors,
      messages,
      canProceed: !hasErrors
    };
  }

  // Real-time validation for form fields
  validateField(
    field: string,
    value: any,
    context: {
      type?: SpecialSetType;
      parameters?: Partial<SpecialSetParameters>;
      exercises?: ExerciseWithSets[];
    }
  ): ValidationMessage[] {
    const messages: ValidationMessage[] = [];

    switch (field) {
      case 'rounds':
        if (context.type && typeof value === 'number') {
          const rules = SPECIAL_SET_VALIDATION_RULES[context.type];
          if (rules && 'rounds' in rules) {
            const { min, max } = rules.rounds;
            if (value < min || value > max) {
              messages.push({
                id: this.generateId(),
                level: 'error',
                message: `Rounds must be between ${min} and ${max}`,
                field
              });
            }
          }
        }
        break;

      case 'restBetweenSets':
        if (typeof value === 'number') {
          if (value < 0) {
            messages.push({
              id: this.generateId(),
              level: 'error',
              message: 'Rest time cannot be negative',
              field
            });
          } else if (value > 600) {
            messages.push({
              id: this.generateId(),
              level: 'warning',
              message: 'Rest time over 10 minutes is unusually long',
              field
            });
          }
        }
        break;

      case 'dropPercentage':
        if (typeof value === 'number') {
          if (value < 5 || value > 70) {
            messages.push({
              id: this.generateId(),
              level: 'error',
              message: 'Drop percentage must be between 5% and 70%',
              field
            });
          }
        }
        break;
    }

    return messages;
  }
}

export const specialSetValidator = new SpecialSetValidator();
