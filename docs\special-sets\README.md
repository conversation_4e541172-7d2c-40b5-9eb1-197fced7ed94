# Special Sets System Documentation

## Overview

The Special Sets system provides advanced workout functionality including supersets, circuits, dropsets, and rest-pause sets. It features intelligent validation, real-time feedback, performance optimization, and comprehensive accessibility support.

## Table of Contents

- [Quick Start](#quick-start)
- [Core Concepts](#core-concepts)
- [API Reference](#api-reference)
- [Components](#components)
- [Hooks](#hooks)
- [Examples](#examples)
- [Performance](#performance)
- [Accessibility](#accessibility)
- [Testing](#testing)

## Quick Start

### Basic Usage

```tsx
import { SpecialSetsProvider, SpecialSetsIntegration } from '@/components/workout/special-sets';

function WorkoutPage() {
  const exercises = [
    {
      id: 'ex1',
      name: 'Push-ups',
      sets: [{ id: 'set1', weight: 0, reps: 10, isExecuted: false }]
    },
    {
      id: 'ex2',
      name: 'Pull-ups',
      sets: [{ id: 'set2', weight: 0, reps: 8, isExecuted: false }]
    }
  ];

  return (
    <SpecialSetsProvider workoutId="workout-1">
      <SpecialSetsIntegration
        exercises={exercises}
        workoutId="workout-1"
        onSetExecuted={(exerciseIndex, setIndex) => {
          console.log('Set executed:', exerciseIndex, setIndex);
        }}
      />
    </SpecialSetsProvider>
  );
}
```

### Creating a Superset

```tsx
import { useSpecialSetActions } from '@/contexts/SpecialSetsContext';

function CreateSupersetExample() {
  const { createSpecialSet } = useSpecialSetActions();

  const handleCreateSuperset = async () => {
    await createSpecialSet(
      'superset',
      {
        type: 'superset',
        rounds: 3,
        restBetweenExercises: 0,
        restBetweenSets: 90,
        exercises: ['ex1', 'ex2']
      },
      ['ex1', 'ex2']
    );
  };

  return (
    <button onClick={handleCreateSuperset}>
      Create Superset
    </button>
  );
}
```

## Core Concepts

### Special Set Types

#### Superset
Combines 2-4 exercises performed back-to-back with minimal rest between exercises.

```typescript
type SupersetParameters = {
  type: 'superset';
  rounds: number; // 1-10
  restBetweenExercises: number; // 0-300 seconds
  restBetweenSets: number; // 0-600 seconds
  exercises: string[]; // 2-4 exercise IDs
};
```

#### Circuit
Multiple exercises performed in sequence with timed work and rest periods.

```typescript
type CircuitParameters = {
  type: 'circuit';
  workTime: number; // 10-300 seconds
  restTime: number; // 0-120 seconds
  rounds: number; // 1-10
  restBetweenRounds: number; // 0-600 seconds
  exercises: string[]; // 3-8 exercise IDs
};
```

#### Dropset
Reduces weight after reaching failure to continue the exercise.

```typescript
type DropsetParameters = {
  type: 'dropset';
  dropType: 'single' | 'double' | 'triple';
  dropPercentages: number[]; // 5-70%, 1-3 drops
  restBetweenDrops: number; // 0-60 seconds
  exerciseId: string; // Single exercise only
};
```

#### Rest-Pause
Brief rests during a set to perform additional repetitions.

```typescript
type RestPauseParameters = {
  type: 'restpause';
  restPauseDuration: number; // 5-60 seconds
  maxReps: number; // 1-50
  targetReps: number; // 1-100
  exerciseId: string; // Single exercise only
};
```

### Data Flow

The system uses a unidirectional data flow with optimized state management:

1. **Context Provider** - Manages global state and provides actions
2. **Data Manager** - Handles exercise data with batched updates
3. **Cache Layer** - Provides intelligent caching with TTL
4. **API Layer** - Handles server communication
5. **Validation Layer** - Real-time validation with user feedback

## API Reference

### REST Endpoints

#### Special Sets

```
GET    /api/special-sets              # List special sets
POST   /api/special-sets              # Create special set
GET    /api/special-sets/:id          # Get special set
PUT    /api/special-sets/:id          # Update special set
DELETE /api/special-sets/:id          # Delete special set
```

#### Templates

```
GET    /api/special-sets/templates    # List templates
POST   /api/special-sets/templates    # Create template
PUT    /api/special-sets/templates    # Update template
DELETE /api/special-sets/templates    # Delete template
```

#### Executions

```
GET    /api/special-sets/executions   # List executions
POST   /api/special-sets/executions   # Record execution
DELETE /api/special-sets/executions   # Clear executions
```

#### Ratings

```
GET    /api/special-sets/templates/:id/ratings  # Get ratings
POST   /api/special-sets/templates/:id/ratings  # Rate template
DELETE /api/special-sets/templates/:id/ratings  # Remove rating
```

### Request/Response Examples

#### Create Special Set

```typescript
// Request
POST /api/special-sets
{
  "workoutId": "workout-1",
  "type": "superset",
  "parameters": {
    "type": "superset",
    "rounds": 3,
    "restBetweenExercises": 0,
    "restBetweenSets": 90,
    "exercises": ["ex1", "ex2"]
  },
  "exerciseIds": ["ex1", "ex2"]
}

// Response
{
  "id": "special-set-1",
  "userId": "user-1",
  "workoutId": "workout-1",
  "type": "superset",
  "parameters": { /* ... */ },
  "exerciseIds": ["ex1", "ex2"],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

## Components

### SpecialSetsProvider

Context provider that manages special sets state and provides actions.

```tsx
interface SpecialSetsProviderProps {
  children: React.ReactNode;
  workoutId?: string;
  options?: {
    enableCaching?: boolean;
    enableRealTimeSync?: boolean;
    batchUpdates?: boolean;
  };
}
```

### SpecialSetsIntegration

Main component that integrates special sets functionality into workouts.

```tsx
interface SpecialSetsIntegrationProps {
  exercises: ExerciseWithSets[];
  workoutId: string;
  onSetExecuted: (exerciseIndex: number, setIndex: number) => void;
  onSetUpdated?: (exerciseIndex: number, setIndex: number, field: string, value: any) => void;
  userLevel?: 'beginner' | 'intermediate' | 'advanced';
  workoutGoal?: 'strength' | 'hypertrophy' | 'endurance' | 'fat-loss';
  timeAvailable?: number;
}
```

### SpecialSetDisplay

Displays and manages execution of special sets with timer and progress tracking.

```tsx
interface SpecialSetDisplayProps {
  type: SpecialSetType;
  exercises: ExerciseWithSets[];
  execution?: SpecialSetExecution;
  onSetExecuted: (exerciseIndex: number, setIndex: number) => void;
  onSetUpdated?: (exerciseIndex: number, setIndex: number, field: string, value: any) => void;
  enhanced?: boolean;
  showInstructions?: boolean;
  showTimer?: boolean;
}
```

## Hooks

### useSpecialSetsContext

Main hook for accessing special sets functionality.

```tsx
const {
  exercises,
  specialSets,
  templates,
  specialSetGroups,
  isLoading,
  createSpecialSet,
  updateSpecialSet,
  deleteSpecialSet,
  // ... more actions and state
} = useSpecialSetsContext();
```

### useSpecialSetActions

Hook for special sets actions only.

```tsx
const {
  createSpecialSet,
  updateSpecialSet,
  deleteSpecialSet,
  updateExercise,
  setExercises,
  loadTemplates,
  refreshData,
  flushPendingUpdates,
  clearCache
} = useSpecialSetActions();
```

### useSpecialSetExercises

Hook for exercise-related data.

```tsx
const {
  allExercises,
  standaloneExercises,
  exercisesInSpecialSets
} = useSpecialSetExercises();
```

### useSpecialSetGroups

Hook for special set group management.

```tsx
const {
  groups,
  groupsArray,
  getGroup
} = useSpecialSetGroups();
```

### useSpecialSetState

Hook for loading and error states.

```tsx
const {
  isLoading,
  isCreating,
  isUpdating,
  errors,
  pendingUpdatesCount,
  cacheStats
} = useSpecialSetState();
```

### useOptimizedSpecialSets

Advanced hook with performance optimizations.

```tsx
const optimizedData = useOptimizedSpecialSets({
  workoutId: 'workout-1',
  enableCaching: true,
  enableRealTimeSync: true,
  batchUpdates: true
});
```

## Examples

### Complete Superset Example

```tsx
import React from 'react';
import { SpecialSetsProvider, useSpecialSetActions, useSpecialSetState } from '@/contexts/SpecialSetsContext';

function SupersetWorkout() {
  const { createSpecialSet } = useSpecialSetActions();
  const { isCreating } = useSpecialSetState();

  const exercises = [
    {
      id: 'pushups',
      name: 'Push-ups',
      primaryMuscleGroup: 'chest',
      sets: [
        { id: 'set1', weight: 0, reps: 15, isExecuted: false },
        { id: 'set2', weight: 0, reps: 12, isExecuted: false },
        { id: 'set3', weight: 0, reps: 10, isExecuted: false }
      ]
    },
    {
      id: 'pullups',
      name: 'Pull-ups',
      primaryMuscleGroup: 'back',
      sets: [
        { id: 'set4', weight: 0, reps: 8, isExecuted: false },
        { id: 'set5', weight: 0, reps: 6, isExecuted: false },
        { id: 'set6', weight: 0, reps: 5, isExecuted: false }
      ]
    }
  ];

  const handleCreateSuperset = async () => {
    try {
      await createSpecialSet(
        'superset',
        {
          type: 'superset',
          rounds: 3,
          restBetweenExercises: 10, // 10 seconds between exercises
          restBetweenSets: 120, // 2 minutes between supersets
          exercises: ['pushups', 'pullups']
        },
        ['pushups', 'pullups']
      );
      console.log('Superset created successfully!');
    } catch (error) {
      console.error('Failed to create superset:', error);
    }
  };

  return (
    <div className="space-y-4">
      <h2>Upper Body Superset</h2>
      <p>Combine push-ups and pull-ups for an effective upper body workout.</p>
      
      <button 
        onClick={handleCreateSuperset}
        disabled={isCreating}
        className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
      >
        {isCreating ? 'Creating...' : 'Create Push/Pull Superset'}
      </button>
    </div>
  );
}

function App() {
  return (
    <SpecialSetsProvider workoutId="upper-body-workout">
      <SupersetWorkout />
    </SpecialSetsProvider>
  );
}
```

### Circuit Training Example

```tsx
function CircuitTraining() {
  const { createSpecialSet } = useSpecialSetActions();

  const circuitExercises = [
    { id: 'burpees', name: 'Burpees', sets: [{ id: 's1', reps: 10, isExecuted: false }] },
    { id: 'squats', name: 'Jump Squats', sets: [{ id: 's2', reps: 15, isExecuted: false }] },
    { id: 'pushups', name: 'Push-ups', sets: [{ id: 's3', reps: 12, isExecuted: false }] },
    { id: 'lunges', name: 'Lunges', sets: [{ id: 's4', reps: 20, isExecuted: false }] },
    { id: 'plank', name: 'Plank', sets: [{ id: 's5', duration: 30, isExecuted: false }] }
  ];

  const createHIITCircuit = async () => {
    await createSpecialSet(
      'circuit',
      {
        type: 'circuit',
        workTime: 45, // 45 seconds work
        restTime: 15, // 15 seconds rest
        rounds: 4, // 4 rounds
        restBetweenRounds: 180, // 3 minutes between rounds
        exercises: circuitExercises.map(ex => ex.id)
      },
      circuitExercises.map(ex => ex.id)
    );
  };

  return (
    <div>
      <h3>HIIT Circuit</h3>
      <p>High-intensity interval training with 5 exercises</p>
      <button onClick={createHIITCircuit}>
        Create HIIT Circuit
      </button>
    </div>
  );
}
```

### Dropset Example

```tsx
function DropsetExample() {
  const { createSpecialSet } = useSpecialSetActions();

  const benchPress = {
    id: 'bench-press',
    name: 'Bench Press',
    sets: [
      { id: 'set1', weight: 100, reps: 8, isExecuted: false }
    ]
  };

  const createDropset = async () => {
    await createSpecialSet(
      'dropset',
      {
        type: 'dropset',
        dropType: 'double', // Two drops
        dropPercentages: [20, 30], // Drop 20%, then 30%
        restBetweenDrops: 10, // 10 seconds between drops
        exerciseId: 'bench-press'
      },
      ['bench-press']
    );
  };

  return (
    <div>
      <h3>Bench Press Dropset</h3>
      <p>Perform bench press with two weight drops for muscle exhaustion</p>
      <button onClick={createDropset}>
        Create Dropset
      </button>
    </div>
  );
}
```

## Performance

### Optimization Features

- **Intelligent Caching**: TTL-based caching with automatic eviction
- **Batched Updates**: Groups multiple state updates for efficiency
- **Lazy Loading**: Components and data loaded on demand
- **Memoization**: Expensive calculations cached automatically
- **Virtual Scrolling**: Handles large exercise lists efficiently

### Performance Monitoring

```tsx
import { useSpecialSetState } from '@/contexts/SpecialSetsContext';

function PerformanceMonitor() {
  const { cacheStats, pendingUpdatesCount } = useSpecialSetState();

  return (
    <div className="performance-stats">
      <div>Cache Hit Rate: {(cacheStats.hitRate * 100).toFixed(1)}%</div>
      <div>Cache Size: {cacheStats.size} items</div>
      <div>Pending Updates: {pendingUpdatesCount}</div>
    </div>
  );
}
```

## Accessibility

### WCAG 2.1 AA Compliance

- **Keyboard Navigation**: Full keyboard support with logical tab order
- **Screen Reader Support**: Comprehensive ARIA labels and live regions
- **Color Contrast**: Meets WCAG contrast requirements
- **Touch Targets**: Minimum 44px touch target size
- **Reduced Motion**: Respects user motion preferences

### Accessibility Features

```tsx
import { useAccessibility } from '@/lib/accessibility/special-sets-a11y';

function AccessibleSpecialSets() {
  const { announce, handleKeyboardShortcuts, prefersReducedMotion } = useAccessibility();

  React.useEffect(() => {
    return handleKeyboardShortcuts({
      createSpecialSet: () => {
        announce('Opening special set creation dialog');
        // Open modal
      },
      startTimer: () => {
        announce('Timer started');
        // Start timer
      }
    });
  }, []);

  return (
    <div>
      {/* Accessible special sets UI */}
    </div>
  );
}
```

## Testing

### Running Tests

```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# Performance tests
npm run test:performance

# E2E tests
npm run test:e2e

# All tests
npm run test
```

### Test Coverage

The system maintains >90% test coverage across:

- Unit tests for all validation logic
- Integration tests for complete workflows
- Performance tests for optimization verification
- Accessibility tests for WCAG compliance
- E2E tests for user scenarios

### Example Test

```typescript
import { specialSetValidator } from '@/lib/validation/special-sets-validation';

describe('Special Set Validation', () => {
  it('should validate superset parameters', () => {
    const result = specialSetValidator.validateComplete(
      'superset',
      {
        type: 'superset',
        rounds: 3,
        restBetweenExercises: 0,
        restBetweenSets: 90,
        exercises: ['ex1', 'ex2']
      },
      mockExercises
    );

    expect(result.isValid).toBe(true);
    expect(result.canProceed).toBe(true);
  });
});
```

## Contributing

See [CONTRIBUTING.md](./CONTRIBUTING.md) for development guidelines and contribution instructions.

## License

This project is licensed under the MIT License - see [LICENSE.md](./LICENSE.md) for details.
