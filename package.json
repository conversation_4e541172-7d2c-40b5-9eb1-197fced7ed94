{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 9001", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:debug": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "deploy:storage": "firebase deploy --only storage", "deploy:cors": "gsutil cors set cors.json gs://gymzy-launch.appspot.com"}, "dependencies": {"@genkit-ai/googleai": "^1.8.0", "@genkit-ai/next": "^1.8.0", "@hookform/resolvers": "^4.1.3", "@langchain/core": "^0.1.40", "@langchain/groq": "^0.0.14", "@langchain/langgraph": "^0.0.13", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@react-three/drei": "^9.109.2", "@react-three/fiber": "^8.16.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.9.0", "framer-motion": "^12.16.0", "genkit": "^1.8.0", "groq-sdk": "^0.3.3", "langchain": "^0.1.14", "lucide-react": "^0.475.0", "next": "15.2.3", "next-cloudinary": "^6.16.0", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "three": "^0.167.0", "zod": "^3.24.2"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/three": "^0.167.1", "@types/webpack": "^5.28.5", "genkit-cli": "^1.8.0", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "postcss": "^8", "raw-loader": "^4.0.2", "tailwindcss": "^3.4.1", "typescript": "^5", "webpack": "^5.99.9"}}