{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm ci", "devCommand": "npm run dev", "regions": ["iad1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://gymzy.vercel.app"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "redirects": [{"source": "/home", "destination": "/dashboard", "permanent": true}], "rewrites": [{"source": "/api/health", "destination": "/api/internal/health"}], "env": {"NODE_ENV": "production"}, "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}, "crons": [{"path": "/api/internal/cleanup", "schedule": "0 2 * * *"}]}