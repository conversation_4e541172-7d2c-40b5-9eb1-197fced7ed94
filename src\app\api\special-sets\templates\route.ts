import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp
} from 'firebase/firestore';
import { SpecialSetTemplate } from '@/types/special-sets-unified';
import {
  transformTemplateToDb,
  transformTemplatesFromDb,
  TEMPLATE_COLUMNS
} from '@/lib/database/special-sets-transforms';

export async function GET(request: NextRequest) {
  try {
    // For now, we'll skip authentication since Firebase Auth setup might be different
    // TODO: Implement proper Firebase Auth verification
    const userId = 'temp-user-id'; // This should come from Firebase Auth

    const { searchParams } = new URL(request.url);
    const isPublic = searchParams.get('public') === 'true';
    const difficulty = searchParams.get('difficulty');
    const type = searchParams.get('type');

    // Build Firebase query
    const templatesRef = collection(db, 'special_set_templates');
    let firestoreQuery = query(templatesRef, orderBy('average_rating', 'desc'));

    // Apply filters (Firebase doesn't support complex OR queries like Supabase)
    // For now, we'll fetch all and filter in memory
    const templatesSnapshot = await getDocs(firestoreQuery);
    let templates = templatesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Apply filters
    if (isPublic) {
      templates = templates.filter((template: any) => template.is_public === true);
    } else {
      // Show public templates and user's own templates
      templates = templates.filter((template: any) =>
        template.is_public === true || template.created_by === userId
      );
    }

    if (difficulty) {
      templates = templates.filter((template: any) => template.difficulty === difficulty);
    }

    if (type) {
      templates = templates.filter((template: any) => template.type === type);
    }

    // Transform to response format using transformation layer
    const response = transformTemplatesFromDb(templates || []);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in templates GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // For now, we'll skip authentication since Firebase Auth setup might be different
    // TODO: Implement proper Firebase Auth verification
    const userId = 'temp-user-id'; // This should come from Firebase Auth

    const body = await request.json();
    const { name, description, type, parameters, difficulty, estimatedDuration, targetMuscleGroups, tags } = body;

    // Validate required fields
    if (!name || !type || !parameters) {
      return NextResponse.json(
        { error: 'Missing required fields: name, type, parameters' },
        { status: 400 }
      );
    }

    // Create template data for Firebase
    const templateData = {
      name,
      description: description || '',
      type,
      parameters,
      difficulty: difficulty || 'intermediate',
      estimated_duration: estimatedDuration || 0,
      target_muscle_groups: targetMuscleGroups || [],
      created_by: userId,
      tags: tags || [],
      is_public: false,
      usage_count: 0,
      average_rating: 0,
      rating_count: 0,
      created_at: serverTimestamp(),
      updated_at: serverTimestamp()
    };

    // Insert template into database
    const templatesRef = collection(db, 'special_set_templates');
    const docRef = await addDoc(templatesRef, templateData);

    // Create response
    const response = {
      id: docRef.id,
      ...templateData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error('Error in templates POST:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // For now, we'll skip authentication since Firebase Auth setup might be different
    // TODO: Implement proper Firebase Auth verification
    const userId = 'temp-user-id'; // This should come from Firebase Auth

    const body = await request.json();
    const { id, name, description, difficulty, estimatedDuration, targetMuscleGroups, tags, isPublic } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Check if user owns the template
    const templateRef = doc(db, 'special_set_templates', id);
    const templateSnap = await getDoc(templateRef);

    if (!templateSnap.exists()) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    const existingTemplate = templateSnap.data();
    if (existingTemplate?.created_by !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized to modify this template' },
        { status: 403 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updated_at: serverTimestamp(),
    };

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (difficulty !== undefined) updateData.difficulty = difficulty;
    if (estimatedDuration !== undefined) updateData.estimated_duration = estimatedDuration;
    if (targetMuscleGroups !== undefined) updateData.target_muscle_groups = targetMuscleGroups;
    if (tags !== undefined) updateData.tags = tags;
    if (isPublic !== undefined) updateData.is_public = isPublic;

    // Update template
    try {
      await updateDoc(templateRef, updateData);

      // Get updated template
      const updatedSnap = await getDoc(templateRef);
      const updatedTemplate = { id: updatedSnap.id, ...updatedSnap.data() };

      return NextResponse.json(updatedTemplate);
    } catch (updateError) {
      console.error('Error updating template:', updateError);
      return NextResponse.json(
        { error: 'Failed to update template' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in templates PUT:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // For now, we'll skip authentication since Firebase Auth setup might be different
    // TODO: Implement proper Firebase Auth verification
    const userId = 'temp-user-id'; // This should come from Firebase Auth

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Check if user owns the template before deleting
    const templateRef = doc(db, 'special_set_templates', id);
    const templateSnap = await getDoc(templateRef);

    if (!templateSnap.exists()) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    const templateData = templateSnap.data();
    if (templateData?.created_by !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized to delete this template' },
        { status: 403 }
      );
    }

    // Delete template
    try {
      await deleteDoc(templateRef);
      return NextResponse.json({ success: true });
    } catch (deleteError) {
      console.error('Error deleting template:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete template' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in templates DELETE:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
