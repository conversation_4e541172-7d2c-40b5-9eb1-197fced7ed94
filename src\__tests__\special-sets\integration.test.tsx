/**
 * Integration tests for special sets workflow
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SpecialSetsProvider } from '@/contexts/SpecialSetsContext';
import { SpecialSetsIntegration } from '@/components/workout/special-sets/SpecialSetsIntegration';
import { ExerciseWithSets } from '@/types/special-sets-unified';

// Mock the API
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

describe('Special Sets Integration', () => {
  const mockExercises: ExerciseWithSets[] = [
    {
      id: 'ex1',
      name: 'Push-ups',
      primaryMuscleGroup: 'chest',
      sets: [
        { id: 'set1', weight: 0, reps: 10, isExecuted: false },
        { id: 'set2', weight: 0, reps: 10, isExecuted: false }
      ]
    },
    {
      id: 'ex2',
      name: 'Pull-ups',
      primaryMuscleGroup: 'back',
      sets: [
        { id: 'set3', weight: 0, reps: 8, isExecuted: false },
        { id: 'set4', weight: 0, reps: 8, isExecuted: false }
      ]
    },
    {
      id: 'ex3',
      name: 'Squats',
      primaryMuscleGroup: 'legs',
      sets: [
        { id: 'set5', weight: 100, reps: 12, isExecuted: false }
      ]
    }
  ];

  const defaultProps = {
    exercises: mockExercises,
    workoutId: 'workout-1',
    onSetExecuted: vi.fn(),
    onSetUpdated: vi.fn(),
    userLevel: 'intermediate' as const,
    workoutGoal: 'hypertrophy' as const,
    timeAvailable: 30
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve([])
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderWithProvider = (props = {}) => {
    return render(
      <SpecialSetsIntegration {...defaultProps} {...props} />
    );
  };

  describe('Initial Rendering', () => {
    it('should render the special sets integration component', () => {
      renderWithProvider();
      expect(screen.getByText(/create special set/i)).toBeInTheDocument();
    });

    it('should show available exercises count', () => {
      renderWithProvider();
      expect(screen.getByText(/3 exercises available/i)).toBeInTheDocument();
    });

    it('should disable create button when no exercises available', () => {
      renderWithProvider({ exercises: [] });
      const createButton = screen.getByRole('button', { name: /create special set/i });
      expect(createButton).toBeDisabled();
    });
  });

  describe('Special Set Creation Workflow', () => {
    it('should open modal when create button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText(/create special set/i)).toBeInTheDocument();
    });

    it('should allow selecting superset type', async () => {
      const user = userEvent.setup();
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      const supersetOption = screen.getByRole('button', { name: /superset/i });
      await user.click(supersetOption);
      
      expect(screen.getByText(/superset configuration/i)).toBeInTheDocument();
    });

    it('should validate exercise selection for superset', async () => {
      const user = userEvent.setup();
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      const supersetOption = screen.getByRole('button', { name: /superset/i });
      await user.click(supersetOption);
      
      // Try to proceed without selecting exercises
      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);
      
      expect(screen.getByText(/select at least 2 exercises/i)).toBeInTheDocument();
    });

    it('should allow selecting exercises for superset', async () => {
      const user = userEvent.setup();
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      const supersetOption = screen.getByRole('button', { name: /superset/i });
      await user.click(supersetOption);
      
      // Select exercises
      const exercise1 = screen.getByLabelText(/push-ups/i);
      const exercise2 = screen.getByLabelText(/pull-ups/i);
      
      await user.click(exercise1);
      await user.click(exercise2);
      
      expect(exercise1).toBeChecked();
      expect(exercise2).toBeChecked();
    });

    it('should configure superset parameters', async () => {
      const user = userEvent.setup();
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      const supersetOption = screen.getByRole('button', { name: /superset/i });
      await user.click(supersetOption);
      
      // Select exercises
      const exercise1 = screen.getByLabelText(/push-ups/i);
      const exercise2 = screen.getByLabelText(/pull-ups/i);
      await user.click(exercise1);
      await user.click(exercise2);
      
      // Proceed to parameters
      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);
      
      // Configure parameters
      const roundsInput = screen.getByLabelText(/rounds/i);
      const restInput = screen.getByLabelText(/rest between sets/i);
      
      await user.clear(roundsInput);
      await user.type(roundsInput, '4');
      
      await user.clear(restInput);
      await user.type(restInput, '120');
      
      expect(roundsInput).toHaveValue(4);
      expect(restInput).toHaveValue(120);
    });

    it('should create superset successfully', async () => {
      const user = userEvent.setup();
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'special-set-1',
          type: 'superset',
          parameters: {
            type: 'superset',
            rounds: 3,
            restBetweenExercises: 0,
            restBetweenSets: 90,
            exercises: ['ex1', 'ex2']
          }
        })
      });
      
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      const supersetOption = screen.getByRole('button', { name: /superset/i });
      await user.click(supersetOption);
      
      // Select exercises
      const exercise1 = screen.getByLabelText(/push-ups/i);
      const exercise2 = screen.getByLabelText(/pull-ups/i);
      await user.click(exercise1);
      await user.click(exercise2);
      
      // Proceed to parameters
      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);
      
      // Create the special set
      const createFinalButton = screen.getByRole('button', { name: /create superset/i });
      await user.click(createFinalButton);
      
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/special-sets', expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('superset')
        }));
      });
    });
  });

  describe('Circuit Creation', () => {
    it('should allow creating a circuit with time-based parameters', async () => {
      const user = userEvent.setup();
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      const circuitOption = screen.getByRole('button', { name: /circuit/i });
      await user.click(circuitOption);
      
      // Select all exercises for circuit
      const exercise1 = screen.getByLabelText(/push-ups/i);
      const exercise2 = screen.getByLabelText(/pull-ups/i);
      const exercise3 = screen.getByLabelText(/squats/i);
      
      await user.click(exercise1);
      await user.click(exercise2);
      await user.click(exercise3);
      
      // Proceed to parameters
      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);
      
      // Configure circuit parameters
      const workTimeInput = screen.getByLabelText(/work time/i);
      const restTimeInput = screen.getByLabelText(/rest time/i);
      const roundsInput = screen.getByLabelText(/rounds/i);
      
      await user.clear(workTimeInput);
      await user.type(workTimeInput, '45');
      
      await user.clear(restTimeInput);
      await user.type(restTimeInput, '15');
      
      await user.clear(roundsInput);
      await user.type(roundsInput, '3');
      
      expect(workTimeInput).toHaveValue(45);
      expect(restTimeInput).toHaveValue(15);
      expect(roundsInput).toHaveValue(3);
    });
  });

  describe('Dropset Creation', () => {
    it('should only allow single exercise for dropset', async () => {
      const user = userEvent.setup();
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      const dropsetOption = screen.getByRole('button', { name: /dropset/i });
      await user.click(dropsetOption);
      
      // Try to select multiple exercises
      const exercise1 = screen.getByLabelText(/push-ups/i);
      const exercise2 = screen.getByLabelText(/pull-ups/i);
      
      await user.click(exercise1);
      await user.click(exercise2);
      
      // Should show validation error
      expect(screen.getByText(/dropset can only be applied to a single exercise/i)).toBeInTheDocument();
    });

    it('should configure dropset parameters', async () => {
      const user = userEvent.setup();
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      const dropsetOption = screen.getByRole('button', { name: /dropset/i });
      await user.click(dropsetOption);
      
      // Select single exercise
      const exercise1 = screen.getByLabelText(/push-ups/i);
      await user.click(exercise1);
      
      // Proceed to parameters
      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);
      
      // Configure dropset parameters
      const dropTypeSelect = screen.getByLabelText(/drop type/i);
      const dropPercentageInput = screen.getByLabelText(/drop percentage/i);
      
      await user.selectOptions(dropTypeSelect, 'double');
      
      await user.clear(dropPercentageInput);
      await user.type(dropPercentageInput, '25');
      
      expect(dropTypeSelect).toHaveValue('double');
      expect(dropPercentageInput).toHaveValue(25);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const user = userEvent.setup();
      mockFetch.mockRejectedValueOnce(new Error('Network error'));
      
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      const supersetOption = screen.getByRole('button', { name: /superset/i });
      await user.click(supersetOption);
      
      // Select exercises and create
      const exercise1 = screen.getByLabelText(/push-ups/i);
      const exercise2 = screen.getByLabelText(/pull-ups/i);
      await user.click(exercise1);
      await user.click(exercise2);
      
      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);
      
      const createFinalButton = screen.getByRole('button', { name: /create superset/i });
      await user.click(createFinalButton);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to create special set/i)).toBeInTheDocument();
      });
    });

    it('should validate parameters in real-time', async () => {
      const user = userEvent.setup();
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      const supersetOption = screen.getByRole('button', { name: /superset/i });
      await user.click(supersetOption);
      
      // Select exercises
      const exercise1 = screen.getByLabelText(/push-ups/i);
      const exercise2 = screen.getByLabelText(/pull-ups/i);
      await user.click(exercise1);
      await user.click(exercise2);
      
      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);
      
      // Enter invalid rounds
      const roundsInput = screen.getByLabelText(/rounds/i);
      await user.clear(roundsInput);
      await user.type(roundsInput, '0');
      
      expect(screen.getByText(/minimum 1 round/i)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      expect(createButton).toHaveAttribute('aria-label');
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProvider();
      
      // Tab to create button
      await user.tab();
      expect(screen.getByRole('button', { name: /create special set/i })).toHaveFocus();
      
      // Press Enter to open modal
      await user.keyboard('{Enter}');
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('should announce important changes to screen readers', async () => {
      const user = userEvent.setup();
      renderWithProvider();
      
      const createButton = screen.getByRole('button', { name: /create special set/i });
      await user.click(createButton);
      
      // Check for live region announcements
      expect(screen.getByRole('status')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('should not re-render unnecessarily', () => {
      const renderSpy = vi.fn();
      const TestComponent = () => {
        renderSpy();
        return <SpecialSetsIntegration {...defaultProps} />;
      };
      
      const { rerender } = render(<TestComponent />);
      
      // Initial render
      expect(renderSpy).toHaveBeenCalledTimes(1);
      
      // Re-render with same props
      rerender(<TestComponent />);
      
      // Should not cause unnecessary re-renders
      expect(renderSpy).toHaveBeenCalledTimes(2); // Only the test component re-renders
    });

    it('should handle large exercise lists efficiently', () => {
      const largeExerciseList = Array.from({ length: 100 }, (_, i) => ({
        id: `ex${i}`,
        name: `Exercise ${i}`,
        sets: [{ id: `set${i}`, weight: 0, reps: 10, isExecuted: false }]
      }));
      
      const start = performance.now();
      renderWithProvider({ exercises: largeExerciseList });
      const end = performance.now();
      
      // Should render within reasonable time (less than 100ms)
      expect(end - start).toBeLessThan(100);
    });
  });
});
