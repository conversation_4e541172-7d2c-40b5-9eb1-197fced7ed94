/**
 * Optimized hook for special sets management
 * Integrates caching, data flow optimization, and state synchronization
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useSpecialSetStore } from '@/stores/special-sets-store';
import { exerciseDataManager, useExerciseData } from '@/lib/data-flow/exercise-data-manager';
import { specialSetsCache, cachedSpecialSetsAPI } from '@/lib/cache/special-sets-cache';
import { 
  ExerciseWithSets, 
  SpecialSetType, 
  SpecialSetParameters,
  SpecialSetResponse,
  SpecialSetTemplate 
} from '@/types/special-sets-unified';

export interface UseOptimizedSpecialSetsOptions {
  workoutId?: string;
  enableCaching?: boolean;
  enableRealTimeSync?: boolean;
  batchUpdates?: boolean;
}

export interface UseOptimizedSpecialSetsReturn {
  // Data
  exercises: ExerciseWithSets[];
  specialSets: SpecialSetResponse[];
  templates: SpecialSetTemplate[];
  specialSetGroups: Map<string, {
    type: SpecialSetType;
    parameters: SpecialSetParameters;
    exerciseIds: string[];
    order: number[];
  }>;
  
  // State
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  errors: any[];
  pendingUpdatesCount: number;
  cacheStats: {
    hitRate: number;
    size: number;
  };
  
  // Actions
  createSpecialSet: (type: SpecialSetType, parameters: SpecialSetParameters, exerciseIds: string[]) => Promise<void>;
  updateSpecialSet: (id: string, updates: Partial<SpecialSetParameters>) => Promise<void>;
  deleteSpecialSet: (id: string) => Promise<void>;
  updateExercise: (exerciseId: string, field: string, value: any) => void;
  setExercises: (exercises: ExerciseWithSets[]) => void;
  loadTemplates: (filters?: Record<string, any>) => Promise<void>;
  refreshData: () => Promise<void>;
  flushPendingUpdates: () => void;
  clearCache: () => void;
}

export function useOptimizedSpecialSets(
  options: UseOptimizedSpecialSetsOptions = {}
): UseOptimizedSpecialSetsReturn {
  const {
    workoutId,
    enableCaching = true,
    enableRealTimeSync = true,
    batchUpdates = true
  } = options;

  // Store state
  const {
    activeSpecialSets,
    templates,
    isLoading,
    errors,
    createSpecialSet: storeCreateSpecialSet,
    updateSpecialSet: storeUpdateSpecialSet,
    deleteSpecialSet: storeDeleteSpecialSet,
    loadTemplates: storeLoadTemplates
  } = useSpecialSetStore();

  // Local state
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [specialSets, setSpecialSets] = useState<SpecialSetResponse[]>([]);

  // Exercise data from manager using the hook
  const {
    exercises,
    specialSetGroups,
    pendingUpdatesCount,
    updateExercise: dataManagerUpdateExercise,
    createSpecialSetGroup,
    removeSpecialSetGroup,
    flushUpdates
  } = useExerciseData();

  // Exercise data changes are handled by the useExerciseData hook

  // Load special sets for workout
  useEffect(() => {
    if (!workoutId) return;

    const loadSpecialSets = async () => {
      try {
        let data: SpecialSetResponse[];
        
        if (enableCaching) {
          data = await cachedSpecialSetsAPI.getSpecialSets(workoutId);
        } else {
          const response = await fetch(`/api/special-sets?workoutId=${workoutId}`);
          if (!response.ok) throw new Error('Failed to fetch special sets');
          data = await response.json();
        }
        
        setSpecialSets(data);
      } catch (error) {
        console.error('Error loading special sets:', error);
      }
    };

    loadSpecialSets();
  }, [workoutId, enableCaching]);

  // Optimized exercise update function
  const updateExercise = useCallback((exerciseId: string, field: string, value: any) => {
    if (batchUpdates) {
      dataManagerUpdateExercise(exerciseId, field, value);
    } else {
      // Immediate update
      dataManagerUpdateExercise(exerciseId, field, value);
      exerciseDataManager.flushUpdates();
    }
  }, [batchUpdates, dataManagerUpdateExercise]);

  // Set exercises with optimization
  const setExercises = useCallback((newExercises: ExerciseWithSets[]) => {
    exerciseDataManager.setExercises(newExercises);
  }, []);

  // Optimized special set creation
  const createSpecialSet = useCallback(async (
    type: SpecialSetType,
    parameters: SpecialSetParameters,
    exerciseIds: string[]
  ) => {
    if (!workoutId) throw new Error('Workout ID is required');
    
    setIsCreating(true);
    try {
      let result: SpecialSetResponse;
      
      if (enableCaching) {
        result = await cachedSpecialSetsAPI.createSpecialSet({
          workoutId,
          type,
          parameters,
          exerciseIds
        });
      } else {
        await storeCreateSpecialSet(workoutId, type, parameters, exerciseIds);
        // Note: Store doesn't return the created special set, so we'd need to refetch
        return;
      }

      // Create special set group in exercise data manager
      const groupId = result.id;
      createSpecialSetGroup(groupId, type, parameters, exerciseIds);

      // Update local state
      setSpecialSets(prev => [...prev, result]);
      
    } catch (error) {
      console.error('Error creating special set:', error);
      throw error;
    } finally {
      setIsCreating(false);
    }
  }, [workoutId, enableCaching, storeCreateSpecialSet, createSpecialSetGroup]);

  // Optimized special set update
  const updateSpecialSet = useCallback(async (
    id: string,
    updates: Partial<SpecialSetParameters>
  ) => {
    setIsUpdating(true);
    try {
      let result: SpecialSetResponse;
      
      if (enableCaching) {
        result = await cachedSpecialSetsAPI.updateSpecialSet(id, updates);
      } else {
        await storeUpdateSpecialSet(id, updates);
        return;
      }

      // Update local state
      setSpecialSets(prev => prev.map(ss => ss.id === id ? result : ss));
      
    } catch (error) {
      console.error('Error updating special set:', error);
      throw error;
    } finally {
      setIsUpdating(false);
    }
  }, [enableCaching, storeUpdateSpecialSet]);

  // Optimized special set deletion
  const deleteSpecialSet = useCallback(async (id: string) => {
    try {
      if (enableCaching) {
        await cachedSpecialSetsAPI.deleteSpecialSet(id);
      } else {
        await storeDeleteSpecialSet(id);
      }

      // Remove special set group from exercise data manager
      removeSpecialSetGroup(id);

      // Update local state
      setSpecialSets(prev => prev.filter(ss => ss.id !== id));
      
    } catch (error) {
      console.error('Error deleting special set:', error);
      throw error;
    }
  }, [enableCaching, storeDeleteSpecialSet, removeSpecialSetGroup]);

  // Load templates with caching
  const loadTemplates = useCallback(async (filters: Record<string, any> = {}) => {
    try {
      if (enableCaching) {
        const cached = specialSetsCache.getTemplates(filters);
        if (cached) {
          return;
        }
      }

      await storeLoadTemplates();
    } catch (error) {
      console.error('Error loading templates:', error);
      throw error;
    }
  }, [enableCaching, storeLoadTemplates]);

  // Refresh all data
  const refreshData = useCallback(async () => {
    if (enableCaching) {
      // Clear relevant cache entries
      if (workoutId) {
        specialSetsCache.invalidateWorkoutSpecialSets(workoutId);
      }
      specialSetsCache.invalidateAllTemplates();
    }

    // Reload data
    if (workoutId) {
      const response = await fetch(`/api/special-sets?workoutId=${workoutId}`);
      if (response.ok) {
        const data = await response.json();
        setSpecialSets(data);
      }
    }

    await loadTemplates();
  }, [enableCaching, workoutId, loadTemplates]);

  // Flush pending updates
  const flushPendingUpdates = useCallback(() => {
    exerciseDataManager.flushUpdates();
  }, []);

  // Clear cache
  const clearCache = useCallback(() => {
    if (enableCaching) {
      specialSetsCache.clear();
    }
  }, [enableCaching]);

  // Cache statistics
  const cacheStats = useMemo(() => {
    if (!enableCaching) {
      return { hitRate: 0, size: 0 };
    }
    
    return {
      hitRate: specialSetsCache.getHitRate(),
      size: specialSetsCache.getCacheSize()
    };
  }, [enableCaching]);

  // Convert active special sets to array
  const activeSpecialSetsArray = useMemo(() => {
    return Array.from(activeSpecialSets.values());
  }, [activeSpecialSets]);

  return {
    // Data
    exercises: Array.from(exercises.values()),
    specialSets: enableCaching ? specialSets : activeSpecialSetsArray,
    templates: Array.from(templates.values()),
    specialSetGroups,
    
    // State
    isLoading,
    isCreating,
    isUpdating,
    errors,
    pendingUpdatesCount,
    cacheStats,
    
    // Actions
    createSpecialSet,
    updateSpecialSet,
    deleteSpecialSet,
    updateExercise,
    setExercises,
    loadTemplates,
    refreshData,
    flushPendingUpdates,
    clearCache
  };
}
