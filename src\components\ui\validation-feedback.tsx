import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertCircle, CheckCircle, AlertTriangle, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

export type ValidationLevel = 'error' | 'warning' | 'success' | 'info';

export interface ValidationMessage {
  id: string;
  level: ValidationLevel;
  message: string;
  field?: string;
}

interface ValidationFeedbackProps {
  messages: ValidationMessage[];
  className?: string;
  showIcons?: boolean;
  compact?: boolean;
}

const levelConfig = {
  error: {
    icon: AlertCircle,
    className: 'text-red-600 bg-red-50 border-red-200',
    iconClassName: 'text-red-500'
  },
  warning: {
    icon: AlertTriangle,
    className: 'text-amber-600 bg-amber-50 border-amber-200',
    iconClassName: 'text-amber-500'
  },
  success: {
    icon: CheckCircle,
    className: 'text-green-600 bg-green-50 border-green-200',
    iconClassName: 'text-green-500'
  },
  info: {
    icon: Info,
    className: 'text-blue-600 bg-blue-50 border-blue-200',
    iconClassName: 'text-blue-500'
  }
};

export function ValidationFeedback({
  messages,
  className,
  showIcons = true,
  compact = false
}: ValidationFeedbackProps) {
  if (messages.length === 0) return null;

  return (
    <div className={cn('space-y-2', className)}>
      <AnimatePresence mode="popLayout">
        {messages.map((message) => {
          const config = levelConfig[message.level];
          const Icon = config.icon;

          return (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className={cn(
                'flex items-start gap-2 p-3 rounded-lg border text-sm',
                config.className,
                compact && 'p-2 text-xs'
              )}
            >
              {showIcons && (
                <Icon className={cn('h-4 w-4 mt-0.5 flex-shrink-0', config.iconClassName)} />
              )}
              <div className="flex-1">
                {message.field && (
                  <span className="font-medium">{message.field}: </span>
                )}
                <span>{message.message}</span>
              </div>
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );
}

interface FieldValidationProps {
  messages: ValidationMessage[];
  fieldName: string;
  className?: string;
}

export function FieldValidation({ messages, fieldName, className }: FieldValidationProps) {
  const fieldMessages = messages.filter(msg => msg.field === fieldName);
  
  return (
    <ValidationFeedback 
      messages={fieldMessages} 
      className={className}
      compact={true}
      showIcons={true}
    />
  );
}

interface ValidationSummaryProps {
  messages: ValidationMessage[];
  title?: string;
  className?: string;
}

export function ValidationSummary({ messages, title = "Validation Issues", className }: ValidationSummaryProps) {
  const errorCount = messages.filter(m => m.level === 'error').length;
  const warningCount = messages.filter(m => m.level === 'warning').length;
  
  if (messages.length === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        'p-4 rounded-lg border bg-gray-50 border-gray-200',
        className
      )}
    >
      <div className="flex items-center gap-2 mb-3">
        <AlertCircle className="h-5 w-5 text-gray-600" />
        <h3 className="font-medium text-gray-900">{title}</h3>
        <div className="flex gap-2 ml-auto text-xs">
          {errorCount > 0 && (
            <span className="px-2 py-1 bg-red-100 text-red-700 rounded">
              {errorCount} error{errorCount !== 1 ? 's' : ''}
            </span>
          )}
          {warningCount > 0 && (
            <span className="px-2 py-1 bg-amber-100 text-amber-700 rounded">
              {warningCount} warning{warningCount !== 1 ? 's' : ''}
            </span>
          )}
        </div>
      </div>
      <ValidationFeedback messages={messages} compact={true} />
    </motion.div>
  );
}

// Hook for managing validation state
export function useValidation() {
  const [messages, setMessages] = React.useState<ValidationMessage[]>([]);

  const addMessage = React.useCallback((message: Omit<ValidationMessage, 'id'>) => {
    const id = `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    setMessages(prev => [...prev, { ...message, id }]);
  }, []);

  const removeMessage = React.useCallback((id: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== id));
  }, []);

  const clearMessages = React.useCallback((field?: string) => {
    if (field) {
      setMessages(prev => prev.filter(msg => msg.field !== field));
    } else {
      setMessages([]);
    }
  }, []);

  const hasErrors = React.useMemo(() => 
    messages.some(msg => msg.level === 'error'), [messages]
  );

  const hasWarnings = React.useMemo(() => 
    messages.some(msg => msg.level === 'warning'), [messages]
  );

  const getFieldMessages = React.useCallback((field: string) => 
    messages.filter(msg => msg.field === field), [messages]
  );

  const validateField = React.useCallback((
    field: string, 
    value: any, 
    validators: Array<(value: any) => ValidationMessage | null>
  ) => {
    // Clear existing messages for this field
    clearMessages(field);

    // Run validators
    validators.forEach(validator => {
      const result = validator(value);
      if (result) {
        addMessage({ ...result, field });
      }
    });
  }, [addMessage, clearMessages]);

  return {
    messages,
    addMessage,
    removeMessage,
    clearMessages,
    hasErrors,
    hasWarnings,
    getFieldMessages,
    validateField
  };
}

// Common validators
export const validators = {
  required: (message = 'This field is required') => (value: any): ValidationMessage | null => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return { id: '', level: 'error', message };
    }
    return null;
  },

  minLength: (min: number, message?: string) => (value: string): ValidationMessage | null => {
    if (value && value.length < min) {
      return { 
        id: '', 
        level: 'error', 
        message: message || `Must be at least ${min} characters` 
      };
    }
    return null;
  },

  maxLength: (max: number, message?: string) => (value: string): ValidationMessage | null => {
    if (value && value.length > max) {
      return { 
        id: '', 
        level: 'error', 
        message: message || `Must be no more than ${max} characters` 
      };
    }
    return null;
  },

  range: (min: number, max: number, message?: string) => (value: number): ValidationMessage | null => {
    if (value !== undefined && (value < min || value > max)) {
      return { 
        id: '', 
        level: 'error', 
        message: message || `Must be between ${min} and ${max}` 
      };
    }
    return null;
  },

  positive: (message = 'Must be a positive number') => (value: number): ValidationMessage | null => {
    if (value !== undefined && value <= 0) {
      return { id: '', level: 'error', message };
    }
    return null;
  },

  integer: (message = 'Must be a whole number') => (value: number): ValidationMessage | null => {
    if (value !== undefined && !Number.isInteger(value)) {
      return { id: '', level: 'error', message };
    }
    return null;
  }
};
