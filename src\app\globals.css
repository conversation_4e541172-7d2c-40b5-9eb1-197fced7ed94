@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 99%; /* #FDFFFC - Light background */
    --foreground: 270 50% 12%; /* #34113F - Dark text */
    --card: 0 0% 100%; /* White cards */
    --card-foreground: 270 50% 12%; /* Dark text on cards */
    --popover: 0 0% 100%; /* White popover */
    --popover-foreground: 270 50% 12%; /* Dark text on popover */
    --primary: 270 50% 12%; /* #34113F */
    --primary-foreground: 0 0% 98%; /* White text on primary */
    --secondary: 146 25% 56%; /* #73AB84 */
    --secondary-foreground: 0 0% 98%; /* White text on secondary */
    --muted: 0 0% 96%; /* Light muted background */
    --muted-foreground: 0 0% 45%; /* Dark muted text */
    --accent: 354 47% 43%; /* Deep Red #A23B44 */
    --accent-foreground: 0 0% 98%; /* White text on accent */
    --destructive: 0 84% 60%; /* Red for destructive actions */
    --destructive-foreground: 0 0% 98%; /* White text on destructive */
    --border: 0 0% 90%; /* Light border */
    --input: 0 0% 100%; /* White input background */
    --ring: 270 50% 12%; /* Primary color for focus ring */
    --chart-1: hsl(var(--primary));
    --chart-2: hsl(var(--secondary));
    --chart-3: hsl(var(--accent));
    --chart-4: 0 0% 60%;
    --chart-5: 0 0% 40%;
    --radius: 0.5rem;

    /* Sidebar colors for light theme */
    --sidebar-background: 0 0% 98%; /* Very light background */
    --sidebar-foreground: 270 50% 12%; /* Dark text */
    --sidebar-primary: hsl(var(--primary));
    --sidebar-primary-foreground: hsl(var(--primary-foreground));
    --sidebar-accent: hsl(var(--secondary));
    --sidebar-accent-foreground: hsl(var(--secondary-foreground));
    --sidebar-border: 0 0% 90%; /* Light border */
    --sidebar-ring: hsl(var(--ring));
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }
}

/* Enhanced button styles for better visibility and contrast */
@layer components {
  /* Ensure proper button text colors - ONLY for buttons */
  button[class*="bg-primary"]:not([class*="variant-outline"]):not([class*="variant-ghost"]):not([class*="variant-link"]) {
    color: hsl(var(--primary-foreground)) !important;
  }

  button[class*="bg-secondary"]:not([class*="variant-outline"]):not([class*="variant-ghost"]):not([class*="variant-link"]) {
    color: hsl(var(--secondary-foreground)) !important;
  }

  button[class*="bg-accent"]:not([class*="variant-outline"]):not([class*="variant-ghost"]):not([class*="variant-link"]) {
    color: hsl(var(--accent-foreground)) !important;
  }

  /* Specific targeting for default button variant */
  button[data-variant="default"] {
    color: hsl(var(--primary-foreground)) !important;
  }

  /* Ensure disabled buttons are still visible but clearly disabled */
  button:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
  }
}

@layer utilities {
  .rotate-360 {
    transform: rotate(360deg);
  }

  @keyframes flip-3d {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(90deg); }
    100% { transform: rotateY(0deg); }
  }

  .flip-3d {
    animation: flip-3d 0.6s ease-in-out;
  }
}