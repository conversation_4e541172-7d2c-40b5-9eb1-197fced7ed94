/**
 * Unit tests for special sets validation system
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { specialSetValidator } from '@/lib/validation/special-sets-validation';
import { ExerciseWithSets, SpecialSetType, SpecialSetParameters } from '@/types/special-sets-unified';

describe('SpecialSetValidator', () => {
  let mockExercises: ExerciseWithSets[];

  beforeEach(() => {
    mockExercises = [
      {
        id: 'ex1',
        name: 'Push-ups',
        primaryMuscleGroup: 'chest',
        sets: [
          { id: 'set1', weight: 0, reps: 10, isExecuted: false }
        ]
      },
      {
        id: 'ex2',
        name: 'Pull-ups',
        primaryMuscleGroup: 'back',
        sets: [
          { id: 'set2', weight: 0, reps: 8, isExecuted: false }
        ]
      },
      {
        id: 'ex3',
        name: 'Squats',
        primaryMuscleGroup: 'legs',
        sets: [
          { id: 'set3', weight: 100, reps: 12, isExecuted: false }
        ]
      }
    ];
  });

  describe('validateType', () => {
    it('should validate valid special set types', () => {
      const validTypes: SpecialSetType[] = ['superset', 'circuit', 'dropset', 'restpause'];
      
      validTypes.forEach(type => {
        const messages = specialSetValidator.validateType(type);
        expect(messages).toHaveLength(0);
      });
    });

    it('should reject invalid special set types', () => {
      const messages = specialSetValidator.validateType('invalid' as SpecialSetType);
      expect(messages).toHaveLength(1);
      expect(messages[0].level).toBe('error');
      expect(messages[0].message).toContain('Unknown special set type');
    });

    it('should reject empty type', () => {
      const messages = specialSetValidator.validateType('' as SpecialSetType);
      expect(messages).toHaveLength(1);
      expect(messages[0].level).toBe('error');
      expect(messages[0].field).toBe('type');
    });
  });

  describe('validateExerciseSelection', () => {
    it('should validate superset with 2-4 exercises', () => {
      const twoExercises = mockExercises.slice(0, 2);
      const messages = specialSetValidator.validateExerciseSelection(twoExercises, 'superset');
      expect(messages.filter(m => m.level === 'error')).toHaveLength(0);
    });

    it('should reject superset with too few exercises', () => {
      const oneExercise = mockExercises.slice(0, 1);
      const messages = specialSetValidator.validateExerciseSelection(oneExercise, 'superset');
      expect(messages.some(m => m.level === 'error' && m.message.includes('at least 2'))).toBe(true);
    });

    it('should reject superset with too many exercises', () => {
      const fiveExercises = [
        ...mockExercises,
        {
          id: 'ex4',
          name: 'Lunges',
          sets: [{ id: 'set4', weight: 0, reps: 10, isExecuted: false }]
        },
        {
          id: 'ex5',
          name: 'Planks',
          sets: [{ id: 'set5', weight: 0, reps: 1, isExecuted: false }]
        }
      ];
      const messages = specialSetValidator.validateExerciseSelection(fiveExercises, 'superset');
      expect(messages.some(m => m.level === 'error' && m.message.includes('maximum 4'))).toBe(true);
    });

    it('should validate circuit with 3-8 exercises', () => {
      const messages = specialSetValidator.validateExerciseSelection(mockExercises, 'circuit');
      expect(messages.filter(m => m.level === 'error')).toHaveLength(0);
    });

    it('should reject dropset with multiple exercises', () => {
      const messages = specialSetValidator.validateExerciseSelection(mockExercises, 'dropset');
      expect(messages.some(m => m.level === 'error' && m.message.includes('single exercise'))).toBe(true);
    });

    it('should validate dropset with single exercise', () => {
      const oneExercise = mockExercises.slice(0, 1);
      const messages = specialSetValidator.validateExerciseSelection(oneExercise, 'dropset');
      expect(messages.filter(m => m.level === 'error')).toHaveLength(0);
    });

    it('should reject exercises without sets', () => {
      const exerciseWithoutSets: ExerciseWithSets = {
        id: 'ex-no-sets',
        name: 'No Sets Exercise',
        sets: []
      };
      const messages = specialSetValidator.validateExerciseSelection([exerciseWithoutSets], 'superset');
      expect(messages.some(m => m.level === 'error' && m.message.includes('at least one set'))).toBe(true);
    });

    it('should warn about incomplete sets', () => {
      const exerciseWithIncompleteSet: ExerciseWithSets = {
        id: 'ex-incomplete',
        name: 'Incomplete Exercise',
        sets: [{ id: 'incomplete-set', isExecuted: false }] // Missing weight and reps
      };
      const messages = specialSetValidator.validateExerciseSelection([exerciseWithIncompleteSet], 'dropset');
      expect(messages.some(m => m.level === 'warning' && m.message.includes('incomplete set data'))).toBe(true);
    });

    it('should warn about same muscle group in superset', () => {
      const sameMusclExercises: ExerciseWithSets[] = [
        {
          id: 'ex1',
          name: 'Push-ups',
          primaryMuscleGroup: 'chest',
          sets: [{ id: 'set1', weight: 0, reps: 10, isExecuted: false }]
        },
        {
          id: 'ex2',
          name: 'Bench Press',
          primaryMuscleGroup: 'chest',
          sets: [{ id: 'set2', weight: 100, reps: 8, isExecuted: false }]
        }
      ];
      const messages = specialSetValidator.validateExerciseSelection(sameMusclExercises, 'superset');
      expect(messages.some(m => m.level === 'warning' && m.message.includes('different muscle groups'))).toBe(true);
    });
  });

  describe('validateParameters', () => {
    it('should validate valid superset parameters', () => {
      const parameters: SpecialSetParameters = {
        type: 'superset',
        rounds: 3,
        restBetweenExercises: 0,
        restBetweenSets: 90,
        exercises: ['ex1', 'ex2']
      };
      const messages = specialSetValidator.validateParameters(parameters, 'superset');
      expect(messages.filter(m => m.level === 'error')).toHaveLength(0);
    });

    it('should reject invalid round count', () => {
      const parameters: SpecialSetParameters = {
        type: 'superset',
        rounds: 0, // Invalid
        restBetweenExercises: 0,
        restBetweenSets: 90,
        exercises: ['ex1', 'ex2']
      };
      const messages = specialSetValidator.validateParameters(parameters, 'superset');
      expect(messages.some(m => m.level === 'error' && m.message.includes('Minimum 1 round'))).toBe(true);
    });

    it('should warn about excessive rounds', () => {
      const parameters: SpecialSetParameters = {
        type: 'superset',
        rounds: 8, // High but valid
        restBetweenExercises: 0,
        restBetweenSets: 90,
        exercises: ['ex1', 'ex2']
      };
      const messages = specialSetValidator.validateParameters(parameters, 'superset');
      expect(messages.some(m => m.level === 'warning' && m.message.includes('excessive fatigue'))).toBe(true);
    });

    it('should validate circuit parameters', () => {
      const parameters: SpecialSetParameters = {
        type: 'circuit',
        workTime: 45,
        restTime: 15,
        rounds: 3,
        restBetweenRounds: 120,
        exercises: ['ex1', 'ex2', 'ex3']
      };
      const messages = specialSetValidator.validateParameters(parameters, 'circuit');
      expect(messages.filter(m => m.level === 'error')).toHaveLength(0);
    });

    it('should validate dropset parameters', () => {
      const parameters: SpecialSetParameters = {
        type: 'dropset',
        dropType: 'single',
        dropPercentages: [20],
        restBetweenDrops: 10,
        exerciseId: 'ex1'
      };
      const messages = specialSetValidator.validateParameters(parameters, 'dropset');
      expect(messages.filter(m => m.level === 'error')).toHaveLength(0);
    });

    it('should warn about extreme drop percentages', () => {
      const lowDrop: SpecialSetParameters = {
        type: 'dropset',
        dropType: 'single',
        dropPercentages: [5], // Very low
        restBetweenDrops: 10,
        exerciseId: 'ex1'
      };
      const messages = specialSetValidator.validateParameters(lowDrop, 'dropset');
      expect(messages.some(m => m.level === 'warning' && m.message.includes('below 10%'))).toBe(true);

      const highDrop: SpecialSetParameters = {
        type: 'dropset',
        dropType: 'single',
        dropPercentages: [60], // Very high
        restBetweenDrops: 10,
        exerciseId: 'ex1'
      };
      const messages2 = specialSetValidator.validateParameters(highDrop, 'dropset');
      expect(messages2.some(m => m.level === 'warning' && m.message.includes('above 50%'))).toBe(true);
    });

    it('should validate rest-pause parameters', () => {
      const parameters: SpecialSetParameters = {
        type: 'restpause',
        restPauseDuration: 15,
        maxReps: 5,
        targetReps: 20,
        exerciseId: 'ex1'
      };
      const messages = specialSetValidator.validateParameters(parameters, 'restpause');
      expect(messages.filter(m => m.level === 'error')).toHaveLength(0);
    });
  });

  describe('validateComplete', () => {
    it('should return valid result for correct special set', () => {
      const parameters: SpecialSetParameters = {
        type: 'superset',
        rounds: 3,
        restBetweenExercises: 0,
        restBetweenSets: 90,
        exercises: ['ex1', 'ex2']
      };
      const exercises = mockExercises.slice(0, 2);
      
      const result = specialSetValidator.validateComplete('superset', parameters, exercises);
      
      expect(result.isValid).toBe(true);
      expect(result.canProceed).toBe(true);
      expect(result.messages.filter(m => m.level === 'error')).toHaveLength(0);
    });

    it('should return invalid result for incorrect special set', () => {
      const parameters: SpecialSetParameters = {
        type: 'superset',
        rounds: 0, // Invalid
        restBetweenExercises: 0,
        restBetweenSets: 90,
        exercises: ['ex1']
      };
      const exercises = mockExercises.slice(0, 1); // Too few exercises
      
      const result = specialSetValidator.validateComplete('superset', parameters, exercises);
      
      expect(result.isValid).toBe(false);
      expect(result.canProceed).toBe(false);
      expect(result.messages.filter(m => m.level === 'error').length).toBeGreaterThan(0);
    });
  });

  describe('validateField', () => {
    it('should validate rounds field', () => {
      const messages = specialSetValidator.validateField('rounds', 3, { type: 'superset' });
      expect(messages.filter(m => m.level === 'error')).toHaveLength(0);
    });

    it('should reject invalid rounds field', () => {
      const messages = specialSetValidator.validateField('rounds', 0, { type: 'superset' });
      expect(messages.some(m => m.level === 'error')).toBe(true);
    });

    it('should validate rest time field', () => {
      const messages = specialSetValidator.validateField('restBetweenSets', 90, {});
      expect(messages.filter(m => m.level === 'error')).toHaveLength(0);
    });

    it('should reject negative rest time', () => {
      const messages = specialSetValidator.validateField('restBetweenSets', -10, {});
      expect(messages.some(m => m.level === 'error' && m.message.includes('cannot be negative'))).toBe(true);
    });

    it('should warn about excessive rest time', () => {
      const messages = specialSetValidator.validateField('restBetweenSets', 700, {});
      expect(messages.some(m => m.level === 'warning' && m.message.includes('unusually long'))).toBe(true);
    });

    it('should validate drop percentage field', () => {
      const messages = specialSetValidator.validateField('dropPercentage', 25, {});
      expect(messages.filter(m => m.level === 'error')).toHaveLength(0);
    });

    it('should reject invalid drop percentage', () => {
      const messages = specialSetValidator.validateField('dropPercentage', 80, {});
      expect(messages.some(m => m.level === 'error')).toBe(true);
    });
  });
});
