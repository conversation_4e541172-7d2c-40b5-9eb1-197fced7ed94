#!/bin/bash

# Production deployment script for Gymzy
# This script builds and deploys the app to Vercel with production settings

set -e  # Exit on any error

echo "🚀 Starting production deployment for Gymzy..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "📦 Installing Vercel CLI..."
    npm install -g vercel@latest
fi

# Check environment variables
echo "🔍 Checking environment variables..."

required_vars=(
    "NEXT_PUBLIC_FIREBASE_API_KEY"
    "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN"
    "NEXT_PUBLIC_FIREBASE_PROJECT_ID"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ Missing required environment variables:"
    printf '   %s\n' "${missing_vars[@]}"
    echo ""
    echo "Please set these variables in your Vercel project settings or .env.local file."
    echo "You can set them using: vercel env add <variable_name>"
    exit 1
fi

echo "✅ Environment variables check passed"

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf .next
rm -rf .vercel

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Run type checking
echo "🔍 Running type checking..."
npx tsc --noEmit --skipLibCheck

# Run linting
echo "🔍 Running linting..."
npm run lint

# Build the project
echo "🏗️ Building project..."
npm run build

# Deploy to Vercel
echo "🚀 Deploying to Vercel..."
vercel --prod

echo "✅ Production deployment completed successfully!"
echo ""
echo "🌐 Your app should now be available at your Vercel domain"
echo "📊 Check the deployment status in your Vercel dashboard"
