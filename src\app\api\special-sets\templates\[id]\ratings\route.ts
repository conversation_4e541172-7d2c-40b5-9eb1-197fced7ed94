import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  query,
  where,
  orderBy,
  serverTimestamp,
  updateDoc,
  setDoc,
  deleteDoc
} from 'firebase/firestore';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // For now, we'll skip authentication since Firebase Auth setup might be different
    // TODO: Implement proper Firebase Auth verification
    const userId = 'temp-user-id'; // This should come from Firebase Auth

    const { id: templateId } = await params;
    const body = await request.json();
    const { rating, review } = body;

    // Validate rating
    if (!rating || rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // Check if template exists
    const templateRef = doc(db, 'special_set_templates', templateId);
    const templateSnap = await getDoc(templateRef);

    if (!templateSnap.exists()) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Insert or update rating (upsert)
    const ratingsRef = collection(db, 'template_ratings');
    const ratingDocData = {
      template_id: templateId,
      user_id: userId,
      rating,
      review: review || null,
      created_at: serverTimestamp(),
      updated_at: serverTimestamp()
    };

    try {
      await addDoc(ratingsRef, ratingDocData);
    } catch (error) {
      console.error('Error saving rating:', error);
      return NextResponse.json(
        { error: 'Failed to save rating' },
        { status: 500 }
      );
    }

    // Update template average rating
    const ratingsQuery = query(
      ratingsRef,
      where('template_id', '==', templateId)
    );
    const ratingsSnapshot = await getDocs(ratingsQuery);

    if (!ratingsSnapshot.empty) {
      const ratings = ratingsSnapshot.docs.map(doc => doc.data());
      const averageRating = ratings.reduce((sum: number, r: any) => sum + r.rating, 0) / ratings.length;

      await updateDoc(templateRef, {
        average_rating: averageRating,
        rating_count: ratings.length,
        updated_at: serverTimestamp()
      });
    }

    // Transform response
    const response = {
      templateId,
      userId,
      rating,
      review: review || null,
      message: 'Rating saved successfully'
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error('Error in template rating POST:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // For now, we'll skip authentication since Firebase Auth setup might be different
    // TODO: Implement proper Firebase Auth verification
    const userId = 'temp-user-id'; // This should come from Firebase Auth

    const { id: templateId } = await params;
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Check if template exists
    const templateRef = doc(db, 'special_set_templates', templateId);
    const templateSnap = await getDoc(templateRef);

    if (!templateSnap.exists()) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Fetch ratings with pagination
    const ratingsRef = collection(db, 'template_ratings');
    const ratingsQuery = query(
      ratingsRef,
      where('template_id', '==', templateId),
      orderBy('created_at', 'desc')
    );

    const ratingsSnapshot = await getDocs(ratingsQuery);
    const allRatings = ratingsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Apply pagination manually (Firestore doesn't have range like Supabase)
    const paginatedRatings = allRatings.slice(offset, offset + limit);

    // Transform response
    const response = paginatedRatings.map((rating: any) => ({
      id: rating.id,
      templateId: rating.template_id,
      userId: rating.user_id,
      rating: rating.rating,
      review: rating.review,
      createdAt: rating.created_at
    }));

    return NextResponse.json({
      ratings: response,
      total: allRatings.length,
      offset,
      limit
    });
  } catch (error) {
    console.error('Error in template ratings GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // For now, we'll skip authentication since Firebase Auth setup might be different
    // TODO: Implement proper Firebase Auth verification
    const userId = 'temp-user-id'; // This should come from Firebase Auth

    const { id: templateId } = await params;

    // Delete user's rating for this template
    const ratingsRef = collection(db, 'template_ratings');
    const deleteQuery = query(
      ratingsRef,
      where('template_id', '==', templateId),
      where('user_id', '==', userId)
    );

    const deleteSnapshot = await getDocs(deleteQuery);
    const deletePromises = deleteSnapshot.docs.map(doc => deleteDoc(doc.ref));
    await Promise.all(deletePromises);

    // Update template average rating
    const ratingsQuery = query(
      ratingsRef,
      where('template_id', '==', templateId)
    );
    const ratingsSnapshot = await getDocs(ratingsQuery);

    if (!ratingsSnapshot.empty) {
      const ratings = ratingsSnapshot.docs.map(doc => doc.data());
      const averageRating = ratings.length > 0
        ? ratings.reduce((sum: number, r: any) => sum + r.rating, 0) / ratings.length
        : 0;

      const templateRef = doc(db, 'special_set_templates', templateId);
      await updateDoc(templateRef, {
        average_rating: Math.round(averageRating * 100) / 100,
        rating_count: ratings.length,
        updated_at: serverTimestamp()
      });
    }

    return NextResponse.json({
      success: true,
      deletedCount: deleteSnapshot.docs.length
    });
  } catch (error) {
    console.error('Error in template rating DELETE:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
