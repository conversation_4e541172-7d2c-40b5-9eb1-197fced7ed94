ChatContext: loadChatHistory skipped. Loading: false User: null Has Loaded History: false
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\AuthContext.tsx:330 AuthContext: useEffect running.
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\AuthContext.tsx:341 AuthContext: Setting up onAuthStateChanged listener.
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\AuthContext.tsx:345 AuthContext: onAuthStateChanged callback triggered.
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\AuthContext.tsx:348 AuthContext: User detected: wU4wQwifP2XYrgnv84h8viaQFlm1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\AuthContext.tsx:125 AuthContext: Loading user profile for wU4wQwifP2XYrgnv84h8viaQFlm1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\AuthContext.tsx:131 AuthContext: Profile found Object
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\AuthContext.tsx:360 AuthContext: User state set with profile.
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\AuthContext.tsx:374 AuthContext: Loading set to false.
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\ChatContext.tsx:44 ChatContext: Attempting to load chat history for user: wU4wQwifP2XYrgnv84h8viaQFlm1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\ChatContext.tsx:40 ChatContext: loadChatHistory skipped. Loading: true User: Object Has Loaded History: false
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\workout-service.ts:249 No latest workout found.
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\ChatContext.tsx:40 ChatContext: loadChatHistory skipped. Loading: false User: Object Has Loaded History: true
hot-reloader-client.js:242 [Fast Refresh] rebuilding
hot-reloader-client.js:74 [Fast Refresh] done in 205ms
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\chat\page.tsx:176 💬 ChatPage: Sending message to AI service...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:85 💬 ChatService: ===== SENDING CHAT MESSAGE =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:86 💬 ChatService: User ID: wU4wQwifP2XYrgnv84h8viaQFlm1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:87 💬 ChatService: Message: whats the weather today?
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:88 💬 ChatService: History length: 1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:102 🤖 ChatService: Calling agentic AI service with streaming...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:94 🤖 AgenticAI: ===== STARTING RESPONSE GENERATION =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:95 🤖 AgenticAI: User input: whats the weather today?
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:96 🤖 AgenticAI: Chat history length: 1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:97 🤖 AgenticAI: Streaming enabled: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:100 📝 AgenticAI: Building system prompt...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:103 📝 AgenticAI: System prompt length: 1593
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:104 📝 AgenticAI: Contextual prompt length: 297
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:107 🔍 AgenticAI: Getting tool decision...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:258 🔍 AgenticAI: Requesting tool decision from AI...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:272 🔍 AgenticAI: Tool decision prompt length: 2301
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:275 🔍 AgenticAI: Raw tool decision response: ```json
{
  "reasoning": "The user's request is unrelated to fitness.  Providing workout plans or exercise information would be irrelevant and inappropriate.  No fitness tools are needed.",
  "toolCalls": []
}
```
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:280 🔍 AgenticAI: Found JSON in response: {
  "reasoning": "The user's request is unrelated to fitness.  Providing workout plans or exercise information would be irrelevant and inappropriate.  No fitness tools are needed.",
  "toolCalls": []
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:282 🔍 AgenticAI: Parsed tool decision: {
  "reasoning": "The user's request is unrelated to fitness.  Providing workout plans or exercise information would be irrelevant and inappropriate.  No fitness tools are needed.",
  "toolCalls": []
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:111 🔍 AgenticAI: Tool decision received: {
  "reasoning": "The user's request is unrelated to fitness.  Providing workout plans or exercise information would be irrelevant and inappropriate.  No fitness tools are needed.",
  "toolCalls": []
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:159 🛠️ AgenticAI: No tools needed for this request
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:163 📝 AgenticAI: Building final response prompt...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:165 📝 AgenticAI: Final prompt length: 647
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:166 📝 AgenticAI: Final prompt preview: Based on the user's request: "whats the weather today?"

Reasoning: The user's request is unrelated to fitness.  Providing workout plans or exercise information would be irrelevant and inappropriate. ...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:169 🌊 AgenticAI: Using streaming response for final prompt
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:348 🌊 AgenticAI: Starting character streaming...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:349 🌊 AgenticAI: Prompt length for streaming: 647
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-service.ts:121 Starting character streaming for prompt: Based on the user's request: "whats the weather today?"

Reasoning: The user's request is unrelated ...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-service.ts:123 Full response received, length: 172
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: T
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: q
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: "
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ?
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: "
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: .
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: T
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: k
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: b
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: p
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: v
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: .
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: T
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: p
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: p
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: b
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: .
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-service.ts:136 Character streaming completed
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:353 🌊 AgenticAI: Streaming completed successfully
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:354 🌊 AgenticAI: Final streamed content length: 172
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:176 ✅ AgenticAI: Final response generated, length: 172
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:177 ✅ AgenticAI: Response preview: The request "whats the weather today?" is not related to fitness.  Therefore, no workout will be pro...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:186 🎯 AgenticAI: Final result summary:
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:187 🎯 AgenticAI: - Content length: 172
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:188 🎯 AgenticAI: - Tool calls count: 0
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:189 🎯 AgenticAI: - Has workout data: false
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:190 🎯 AgenticAI: - Is streaming: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:191 🤖 AgenticAI: ===== RESPONSE GENERATION COMPLETE =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:115 ✅ ChatService: Agentic AI response received
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:116 ✅ ChatService: Response length: 172
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:117 ✅ ChatService: Tool calls: 0
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:118 ✅ ChatService: Has workout data: false
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\chat\page.tsx:178 💬 ChatPage: AI response received: {message: 'The request "whats the weather today?" is not rela…information, please use a weather app or website.', success: true, toolCalls: Array(0), workoutData: null, isStreaming: true}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\chat\page.tsx:190 💬 ChatPage: Adding AI message with workout data: {role: 'assistant', content: 'The request "whats the weather today?" is not rela…information, please use a weather app or website.', timestamp: Sat Jun 14 2025 03:04:17 GMT+0530 (India Standard Time), workoutData: null}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\chat\page.tsx:176 💬 ChatPage: Sending message to AI service...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:85 💬 ChatService: ===== SENDING CHAT MESSAGE =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:86 💬 ChatService: User ID: wU4wQwifP2XYrgnv84h8viaQFlm1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:87 💬 ChatService: Message: okay now create me a workout
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:88 💬 ChatService: History length: 3
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:102 🤖 ChatService: Calling agentic AI service with streaming...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:94 🤖 AgenticAI: ===== STARTING RESPONSE GENERATION =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:95 🤖 AgenticAI: User input: okay now create me a workout
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:96 🤖 AgenticAI: Chat history length: 3
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:97 🤖 AgenticAI: Streaming enabled: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:100 📝 AgenticAI: Building system prompt...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:103 📝 AgenticAI: System prompt length: 1593
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:104 📝 AgenticAI: Contextual prompt length: 516
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:107 🔍 AgenticAI: Getting tool decision...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:258 🔍 AgenticAI: Requesting tool decision from AI...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:272 🔍 AgenticAI: Tool decision prompt length: 2520
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:275 🔍 AgenticAI: Raw tool decision response: ```json
{
  "reasoning": "The user has requested a workout without specifying any preferences (muscle group, goals, experience level, equipment available).  To create a useful workout, I need more information.  Therefore, I will not use any tools yet, but will instead prompt the user for more details.",
  "toolCalls": []
}
```
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:280 🔍 AgenticAI: Found JSON in response: {
  "reasoning": "The user has requested a workout without specifying any preferences (muscle group, goals, experience level, equipment available).  To create a useful workout, I need more information.  Therefore, I will not use any tools yet, but will instead prompt the user for more details.",
  "toolCalls": []
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:282 🔍 AgenticAI: Parsed tool decision: {
  "reasoning": "The user has requested a workout without specifying any preferences (muscle group, goals, experience level, equipment available).  To create a useful workout, I need more information.  Therefore, I will not use any tools yet, but will instead prompt the user for more details.",
  "toolCalls": []
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:111 🔍 AgenticAI: Tool decision received: {
  "reasoning": "The user has requested a workout without specifying any preferences (muscle group, goals, experience level, equipment available).  To create a useful workout, I need more information.  Therefore, I will not use any tools yet, but will instead prompt the user for more details.",
  "toolCalls": []
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:159 🛠️ AgenticAI: No tools needed for this request
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:163 📝 AgenticAI: Building final response prompt...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:165 📝 AgenticAI: Final prompt length: 766
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:166 📝 AgenticAI: Final prompt preview: Based on the user's request: "okay now create me a workout"

Reasoning: The user has requested a workout without specifying any preferences (muscle group, goals, experience level, equipment available)...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:169 🌊 AgenticAI: Using streaming response for final prompt
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:348 🌊 AgenticAI: Starting character streaming...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:349 🌊 AgenticAI: Prompt length for streaming: 766
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-service.ts:121 Starting character streaming for prompt: Based on the user's request: "okay now create me a workout"

Reasoning: The user has requested a wor...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-service.ts:123 Full response received, length: 467
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: B
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: I
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: k
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: I
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: !
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 

C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: W
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: '
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: v
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ?
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: (
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: B
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: I
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: A
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: v
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: )
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 

C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: W
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: q
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: p
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: v
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ?
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: (
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: B
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: b
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: b
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: .
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: )
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 

C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: W
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ?
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: (
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: S
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: b
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: )
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 

C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: H
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: v
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: k
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ?
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: (
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: .
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: .
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 3
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 0
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: )
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 

C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: O
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: I
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: v
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ,
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: I
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: p
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: z
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: k
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: .
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-service.ts:136 Character streaming completed
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:353 🌊 AgenticAI: Streaming completed successfully
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:354 🌊 AgenticAI: Final streamed content length: 467
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:176 ✅ AgenticAI: Final response generated, length: 467
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:177 ✅ AgenticAI: Response preview: Before I create your workout, I need some information!

* **What's your fitness level?** (Beginner, ...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:186 🎯 AgenticAI: Final result summary:
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:187 🎯 AgenticAI: - Content length: 467
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:188 🎯 AgenticAI: - Tool calls count: 0
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:189 🎯 AgenticAI: - Has workout data: false
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:190 🎯 AgenticAI: - Is streaming: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:191 🤖 AgenticAI: ===== RESPONSE GENERATION COMPLETE =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:115 ✅ ChatService: Agentic AI response received
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:116 ✅ ChatService: Response length: 467
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:117 ✅ ChatService: Tool calls: 0
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:118 ✅ ChatService: Has workout data: false
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\chat\page.tsx:178 💬 ChatPage: AI response received: {message: 'Before I create your workout, I need some informat…ion, I can create a personalized workout for you.', success: true, toolCalls: Array(0), workoutData: null, isStreaming: true}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\chat\page.tsx:190 💬 ChatPage: Adding AI message with workout data: {role: 'assistant', content: 'Before I create your workout, I need some informat…ion, I can create a personalized workout for you.', timestamp: Sat Jun 14 2025 03:04:50 GMT+0530 (India Standard Time), workoutData: null}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\chat\page.tsx:176 💬 ChatPage: Sending message to AI service...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:85 💬 ChatService: ===== SENDING CHAT MESSAGE =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:86 💬 ChatService: User ID: wU4wQwifP2XYrgnv84h8viaQFlm1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:87 💬 ChatService: Message: advanced, i have access to a gym, i want to build muscle, 1 hour
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:88 💬 ChatService: History length: 5
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:102 🤖 ChatService: Calling agentic AI service with streaming...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:94 🤖 AgenticAI: ===== STARTING RESPONSE GENERATION =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:95 🤖 AgenticAI: User input: advanced, i have access to a gym, i want to build muscle, 1 hour
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:96 🤖 AgenticAI: Chat history length: 5
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:97 🤖 AgenticAI: Streaming enabled: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:100 📝 AgenticAI: Building system prompt...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:103 📝 AgenticAI: System prompt length: 1593
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:104 📝 AgenticAI: Contextual prompt length: 1066
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:107 🔍 AgenticAI: Getting tool decision...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:258 🔍 AgenticAI: Requesting tool decision from AI...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:272 🔍 AgenticAI: Tool decision prompt length: 3070
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:275 🔍 AgenticAI: Raw tool decision response: ```json
{
  "reasoning": "The user has specified their fitness level as advanced, access to a gym, a goal of muscle building, and an hour for their workout.  This is sufficient information to create a workout plan. The `create_workout` tool will be used to generate a suitable workout.",
  "toolCalls": [
    {
      "name": "create_workout",
      "parameters": {
        "fitness_level": "Advanced",
        "equipment": "Gym",
        "goal": "Muscle building",
        "duration": "60 minutes"
      }
    }
  ]
}
```
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:280 🔍 AgenticAI: Found JSON in response: {
  "reasoning": "The user has specified their fitness level as advanced, access to a gym, a goal of muscle building, and an hour for their workout.  This is sufficient information to create a workout plan. The `create_workout` tool will be used to generate a suitable workout.",
  "toolCalls": [
    {
      "name": "create_workout",
      "parameters": {
        "fitness_level": "Advanced",
        "equipment": "Gym",
        "goal": "Muscle building",
        "duration": "60 minutes"
      }
    }
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:282 🔍 AgenticAI: Parsed tool decision: {
  "reasoning": "The user has specified their fitness level as advanced, access to a gym, a goal of muscle building, and an hour for their workout.  This is sufficient information to create a workout plan. The `create_workout` tool will be used to generate a suitable workout.",
  "toolCalls": [
    {
      "name": "create_workout",
      "parameters": {
        "fitness_level": "Advanced",
        "equipment": "Gym",
        "goal": "Muscle building",
        "duration": "60 minutes"
      }
    }
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:290 🔧 AgenticAI: Fixing missing exercises in tool call...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:294 🔧 AgenticAI: Added default exercises: (3) [{…}, {…}, {…}]
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:111 🔍 AgenticAI: Tool decision received: {
  "reasoning": "The user has specified their fitness level as advanced, access to a gym, a goal of muscle building, and an hour for their workout.  This is sufficient information to create a workout plan. The `create_workout` tool will be used to generate a suitable workout.",
  "toolCalls": [
    {
      "name": "create_workout",
      "parameters": {
        "fitness_level": "Advanced",
        "equipment": "Gym",
        "goal": "Muscle building",
        "duration": "60 minutes",
        "exercises": [
          {
            "name": "Push-ups",
            "sets": 3,
            "reps": 10,
            "weight": 0
          },
          {
            "name": "Squats",
            "sets": 3,
            "reps": 10,
            "weight": 0
          },
          {
            "name": "Dumbbell Row",
            "sets": 3,
            "reps": 10,
            "weight": 0
          }
        ]
      }
    }
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:119 🛠️ AgenticAI: Executing tools...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:120 🛠️ AgenticAI: Number of tools to execute: 1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:123 🛠️ AgenticAI: Executing tool: create_workout
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:124 🛠️ AgenticAI: Tool parameters: {
  "fitness_level": "Advanced",
  "equipment": "Gym",
  "goal": "Muscle building",
  "duration": "60 minutes",
  "exercises": [
    {
      "name": "Push-ups",
      "sets": 3,
      "reps": 10,
      "weight": 0
    },
    {
      "name": "Squats",
      "sets": 3,
      "reps": 10,
      "weight": 0
    },
    {
      "name": "Dumbbell Row",
      "sets": 3,
      "reps": 10,
      "weight": 0
    }
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:452 🔧 AITool: ===== EXECUTING AI TOOL =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:453 🔧 AITool: Tool name: create_workout
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:454 🔧 AITool: Parameters: {
  "fitness_level": "Advanced",
  "equipment": "Gym",
  "goal": "Muscle building",
  "duration": "60 minutes",
  "exercises": [
    {
      "name": "Push-ups",
      "sets": 3,
      "reps": 10,
      "weight": 0
    },
    {
      "name": "Squats",
      "sets": 3,
      "reps": 10,
      "weight": 0
    },
    {
      "name": "Dumbbell Row",
      "sets": 3,
      "reps": 10,
      "weight": 0
    }
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:461 🏋️ AITool: Executing create_workout...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:199 🏋️ WorkoutTool: ===== EXECUTING CREATE_WORKOUT =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:200 🏋️ WorkoutTool: Raw parameters received: {
  "fitness_level": "Advanced",
  "equipment": "Gym",
  "goal": "Muscle building",
  "duration": "60 minutes",
  "exercises": [
    {
      "name": "Push-ups",
      "sets": 3,
      "reps": 10,
      "weight": 0
    },
    {
      "name": "Squats",
      "sets": 3,
      "reps": 10,
      "weight": 0
    },
    {
      "name": "Dumbbell Row",
      "sets": 3,
      "reps": 10,
      "weight": 0
    }
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:203 🏋️ WorkoutTool: Extracted parameters:
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:204 🏋️ WorkoutTool: - workoutName: undefined
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:205 🏋️ WorkoutTool: - exercises: (3) [{…}, {…}, {…}]
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:206 🏋️ WorkoutTool: - targetMuscles: undefined
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:242 ✅ WorkoutTool: Valid exercises array provided with 3 exercises
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:245 🔄 WorkoutTool: Processing exercises into workout format...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:247 🔄 WorkoutTool: Processing exercise 1: {
  "name": "Push-ups",
  "sets": 3,
  "reps": 10,
  "weight": 0
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:251 🔍 WorkoutTool: Exercise lookup by ID 'undefined': NOT FOUND
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:288 🔍 WorkoutTool: Exercise lookup by name 'Push-ups': FOUND: Push-ups
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:305 ✅ WorkoutTool: Using found exercise data: Push-ups
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:324 ✅ WorkoutTool: Created workout exercise 1: {
  "id": "ai_workout_1749850520601_0",
  "name": "Push-ups",
  "sets": [
    {
      "weight": 0,
      "reps": 10,
      "rpe": 8,
      "isWarmup": false,
      "isExecuted": false
    },
    {
      "weight": 0,
      "reps": 10,
      "rpe": 8,
      "isWarmup": false,
      "isExecuted": false
    },
    {
      "weight": 0,
      "reps": 10,
      "rpe": 8,
      "isWarmup": false,
      "isExecuted": false
    }
  ],
  "muscleGroups": [
    "Pectoralis Major"
  ],
  "equipment": "Mixed",
  "primaryMuscles": [
    "Pectoralis Major"
  ],
  "secondaryMuscles": [
    "Anterior Deltoid",
    "Triceps Brachii"
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:247 🔄 WorkoutTool: Processing exercise 2: {
  "name": "Squats",
  "sets": 3,
  "reps": 10,
  "weight": 0
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:251 🔍 WorkoutTool: Exercise lookup by ID 'undefined': NOT FOUND
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:288 🔍 WorkoutTool: Exercise lookup by name 'Squats': FOUND: Squat
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:305 ✅ WorkoutTool: Using found exercise data: Squat
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:324 ✅ WorkoutTool: Created workout exercise 2: {
  "id": "ai_workout_1749850520601_1",
  "name": "Squat",
  "sets": [
    {
      "weight": 0,
      "reps": 10,
      "rpe": 8,
      "isWarmup": false,
      "isExecuted": false
    },
    {
      "weight": 0,
      "reps": 10,
      "rpe": 8,
      "isWarmup": false,
      "isExecuted": false
    },
    {
      "weight": 0,
      "reps": 10,
      "rpe": 8,
      "isWarmup": false,
      "isExecuted": false
    }
  ],
  "muscleGroups": [
    "Quadriceps",
    "Gluteus Maximus"
  ],
  "equipment": "Mixed",
  "primaryMuscles": [
    "Quadriceps",
    "Gluteus Maximus"
  ],
  "secondaryMuscles": [
    "Hamstrings",
    "Erector Spinae",
    "Gluteus Medius",
    "Adductor Magnus"
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:247 🔄 WorkoutTool: Processing exercise 3: {
  "name": "Dumbbell Row",
  "sets": 3,
  "reps": 10,
  "weight": 0
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:251 🔍 WorkoutTool: Exercise lookup by ID 'undefined': NOT FOUND
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:288 🔍 WorkoutTool: Exercise lookup by name 'Dumbbell Row': FOUND: Dumbbell Row
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:305 ✅ WorkoutTool: Using found exercise data: Dumbbell Row
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:324 ✅ WorkoutTool: Created workout exercise 3: {
  "id": "ai_workout_1749850520601_2",
  "name": "Dumbbell Row",
  "sets": [
    {
      "weight": 0,
      "reps": 10,
      "rpe": 8,
      "isWarmup": false,
      "isExecuted": false
    },
    {
      "weight": 0,
      "reps": 10,
      "rpe": 8,
      "isWarmup": false,
      "isExecuted": false
    },
    {
      "weight": 0,
      "reps": 10,
      "rpe": 8,
      "isWarmup": false,
      "isExecuted": false
    }
  ],
  "muscleGroups": [
    "Latissimus Dorsi"
  ],
  "equipment": "Mixed",
  "primaryMuscles": [
    "Latissimus Dorsi"
  ],
  "secondaryMuscles": [
    "Biceps Brachii",
    "Posterior Deltoid",
    "Lower Trapezius"
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:328 🏋️ WorkoutTool: All exercises processed. Total: 3
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:331 🆔 WorkoutTool: Generated workout ID: ai_workout_1749850520601
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:339 🎯 WorkoutTool: Final result: {
  "success": true,
  "workoutId": "ai_workout_1749850520601",
  "exercises": [
    {
      "id": "ai_workout_1749850520601_0",
      "name": "Push-ups",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Pectoralis Major"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Pectoralis Major"
      ],
      "secondaryMuscles": [
        "Anterior Deltoid",
        "Triceps Brachii"
      ]
    },
    {
      "id": "ai_workout_1749850520601_1",
      "name": "Squat",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Quadriceps",
        "Gluteus Maximus"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Quadriceps",
        "Gluteus Maximus"
      ],
      "secondaryMuscles": [
        "Hamstrings",
        "Erector Spinae",
        "Gluteus Medius",
        "Adductor Magnus"
      ]
    },
    {
      "id": "ai_workout_1749850520601_2",
      "name": "Dumbbell Row",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Latissimus Dorsi"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Latissimus Dorsi"
      ],
      "secondaryMuscles": [
        "Biceps Brachii",
        "Posterior Deltoid",
        "Lower Trapezius"
      ]
    }
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:340 🏋️ WorkoutTool: ===== CREATE_WORKOUT COMPLETE =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:485 ✅ AITool: Tool execution successful
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:486 ✅ AITool: Result: {
  "success": true,
  "workoutId": "ai_workout_1749850520601",
  "exercises": [
    {
      "id": "ai_workout_1749850520601_0",
      "name": "Push-ups",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Pectoralis Major"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Pectoralis Major"
      ],
      "secondaryMuscles": [
        "Anterior Deltoid",
        "Triceps Brachii"
      ]
    },
    {
      "id": "ai_workout_1749850520601_1",
      "name": "Squat",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Quadriceps",
        "Gluteus Maximus"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Quadriceps",
        "Gluteus Maximus"
      ],
      "secondaryMuscles": [
        "Hamstrings",
        "Erector Spinae",
        "Gluteus Medius",
        "Adductor Magnus"
      ]
    },
    {
      "id": "ai_workout_1749850520601_2",
      "name": "Dumbbell Row",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Latissimus Dorsi"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Latissimus Dorsi"
      ],
      "secondaryMuscles": [
        "Biceps Brachii",
        "Posterior Deltoid",
        "Lower Trapezius"
      ]
    }
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-workout-tools.ts:487 🔧 AITool: ===== TOOL EXECUTION COMPLETE =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:128 ✅ AgenticAI: Tool create_workout executed successfully
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:129 ✅ AgenticAI: Tool result: {
  "success": true,
  "workoutId": "ai_workout_1749850520601",
  "exercises": [
    {
      "id": "ai_workout_1749850520601_0",
      "name": "Push-ups",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Pectoralis Major"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Pectoralis Major"
      ],
      "secondaryMuscles": [
        "Anterior Deltoid",
        "Triceps Brachii"
      ]
    },
    {
      "id": "ai_workout_1749850520601_1",
      "name": "Squat",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Quadriceps",
        "Gluteus Maximus"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Quadriceps",
        "Gluteus Maximus"
      ],
      "secondaryMuscles": [
        "Hamstrings",
        "Erector Spinae",
        "Gluteus Medius",
        "Adductor Magnus"
      ]
    },
    {
      "id": "ai_workout_1749850520601_2",
      "name": "Dumbbell Row",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Latissimus Dorsi"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Latissimus Dorsi"
      ],
      "secondaryMuscles": [
        "Biceps Brachii",
        "Posterior Deltoid",
        "Lower Trapezius"
      ]
    }
  ]
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:143 💪 AgenticAI: Workout data extracted: {
  "exercises": [
    {
      "id": "ai_workout_1749850520601_0",
      "name": "Push-ups",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Pectoralis Major"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Pectoralis Major"
      ],
      "secondaryMuscles": [
        "Anterior Deltoid",
        "Triceps Brachii"
      ]
    },
    {
      "id": "ai_workout_1749850520601_1",
      "name": "Squat",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Quadriceps",
        "Gluteus Maximus"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Quadriceps",
        "Gluteus Maximus"
      ],
      "secondaryMuscles": [
        "Hamstrings",
        "Erector Spinae",
        "Gluteus Medius",
        "Adductor Magnus"
      ]
    },
    {
      "id": "ai_workout_1749850520601_2",
      "name": "Dumbbell Row",
      "sets": [
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        },
        {
          "weight": 0,
          "reps": 10,
          "rpe": 8,
          "isWarmup": false,
          "isExecuted": false
        }
      ],
      "muscleGroups": [
        "Latissimus Dorsi"
      ],
      "equipment": "Mixed",
      "primaryMuscles": [
        "Latissimus Dorsi"
      ],
      "secondaryMuscles": [
        "Biceps Brachii",
        "Posterior Deltoid",
        "Lower Trapezius"
      ]
    }
  ],
  "workoutId": "ai_workout_1749850520601"
}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:157 🛠️ AgenticAI: All tools executed. Total successful tool calls: 1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:163 📝 AgenticAI: Building final response prompt...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:165 📝 AgenticAI: Final prompt length: 3381
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:166 📝 AgenticAI: Final prompt preview: Based on the user's request: "advanced, i have access to a gym, i want to build muscle, 1 hour"

I've executed the following tools:
- create_workout: {
  "success": true,
  "workoutId": "ai_workout_17...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:169 🌊 AgenticAI: Using streaming response for final prompt
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:348 🌊 AgenticAI: Starting character streaming...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:349 🌊 AgenticAI: Prompt length for streaming: 3381
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-service.ts:121 Starting character streaming for prompt: Based on the user's request: "advanced, i have access to a gym, i want to build muscle, 1 hour"

I'v...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-service.ts:123 Full response received, length: 369
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: #
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: Y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: -
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: H
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: M
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: -
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: B
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: W
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: k
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 

C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: P
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: -
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: p
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: :
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 3
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 0
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: p
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: (
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: R
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: P
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: E
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 8
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: )
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 

C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: S
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: q
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: :
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 3
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 0
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: p
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: (
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: R
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: P
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: E
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 8
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: )
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 

C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: D
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: b
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: R
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: :
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 3
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 0
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: p
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: (
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: R
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: P
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: E
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 8
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: )
3C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 

2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: N
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: :
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: *
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: R
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: P
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: E
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 8
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: k
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: v
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 2
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: p
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: k
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: .
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: A
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: j
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: l
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: h
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: v
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: c
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: .
2C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: R
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: b
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: m
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: p
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: b
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: f
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: i
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: n
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: g
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: .
3C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: 

C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: R
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: e
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: d
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: s
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: a
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: y
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk:  
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: w
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: r
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: k
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: o
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: u
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: t
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:111 🌊 ChatService: Received streaming chunk: ?
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-service.ts:136 Character streaming completed
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:353 🌊 AgenticAI: Streaming completed successfully
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:354 🌊 AgenticAI: Final streamed content length: 369
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:176 ✅ AgenticAI: Final response generated, length: 369
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:177 ✅ AgenticAI: Response preview: # Your 1-Hour Muscle-Building Workout

* **Push-ups:** 3 sets of 10 reps (RPE 8)
* **Squats:** 3 set...
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:186 🎯 AgenticAI: Final result summary:
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:187 🎯 AgenticAI: - Content length: 369
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:188 🎯 AgenticAI: - Tool calls count: 1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:189 🎯 AgenticAI: - Has workout data: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:190 🎯 AgenticAI: - Is streaming: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\agentic-ai-service.ts:191 🤖 AgenticAI: ===== RESPONSE GENERATION COMPLETE =====
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:115 ✅ ChatService: Agentic AI response received
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:116 ✅ ChatService: Response length: 369
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:117 ✅ ChatService: Tool calls: 1
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\services\ai-chat-service.ts:118 ✅ ChatService: Has workout data: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\chat\page.tsx:178 💬 ChatPage: AI response received: {message: '# Your 1-Hour Muscle-Building Workout\n\n* **Push-up…p before starting.\n\n\nReady to start your workout?', success: true, toolCalls: Array(1), workoutData: {…}, isStreaming: true}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\chat\page.tsx:190 💬 ChatPage: Adding AI message with workout data: {role: 'assistant', content: '# Your 1-Hour Muscle-Building Workout\n\n* **Push-up…p before starting.\n\n\nReady to start your workout?', timestamp: Sat Jun 14 2025 03:05:28 GMT+0530 (India Standard Time), workoutData: {…}}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_0
overrideMethod @ hook.js:608
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6171
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_1
overrideMethod @ hook.js:608
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6171
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_2
overrideMethod @ hook.js:608
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6171
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_0
overrideMethod @ hook.js:600
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6175
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_1
overrideMethod @ hook.js:600
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6175
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_2
overrideMethod @ hook.js:600
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6175
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
2hot-reloader-client.js:242 [Fast Refresh] rebuilding
hot-reloader-client.js:74 [Fast Refresh] done in 32ms
hot-reloader-client.js:242 [Fast Refresh] rebuilding
hot-reloader-client.js:74 [Fast Refresh] done in 25ms
hot-reloader-client.js:74 [Fast Refresh] done in 394ms
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\workout\page.tsx:54 WorkoutPage - currentWorkoutExercises length: 3, hasChanges: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\workout\page.tsx:54 WorkoutPage - currentWorkoutExercises length: 3, hasChanges: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\workout\muscle-activation-svg.tsx:178 MuscleActivationSVG - muscleVolumes prop: {Pectoralis Major: 0, Anterior Deltoid: 0, Lateral Deltoid: 0, Posterior Deltoid: 0, Deltoid: 0, …}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\workout\muscle-activation-svg.tsx:179 MuscleActivationSVG - safeMuscleVolumes: {Pectoralis Major: 0, Anterior Deltoid: 0, Lateral Deltoid: 0, Posterior Deltoid: 0, Deltoid: 0, …}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\workout\muscle-activation-svg.tsx:180 MuscleActivationSVG - relevantMuscles: []
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\workout\muscle-activation-svg.tsx:215 MuscleActivationSVG - scrollElement: Window {window: Window, self: Window, document: document, name: '', location: Location, …}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\ChatContext.tsx:40 ChatContext: loadChatHistory skipped. Loading: false User: {providerId: 'firebase', proactiveRefresh: ProactiveRefresh, reloadUserInfo: {…}, reloadListener: null, uid: 'wU4wQwifP2XYrgnv84h8viaQFlm1', …} Has Loaded History: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\workout\muscle-activation-svg.tsx:178 MuscleActivationSVG - muscleVolumes prop: {Pectoralis Major: 0, Anterior Deltoid: 0, Lateral Deltoid: 0, Posterior Deltoid: 0, Deltoid: 0, …}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\workout\muscle-activation-svg.tsx:179 MuscleActivationSVG - safeMuscleVolumes: {Pectoralis Major: 0, Anterior Deltoid: 0, Lateral Deltoid: 0, Posterior Deltoid: 0, Deltoid: 0, …}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\workout\muscle-activation-svg.tsx:180 MuscleActivationSVG - relevantMuscles: []
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\workout\muscle-activation-svg.tsx:215 MuscleActivationSVG - scrollElement: Window {window: Window, self: Window, document: document, name: '', location: Location, …}
6The resource <URL> was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\ui\dialog.tsx:38 Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
overrideMethod @ hook.js:608
DescriptionWarning.useEffect @ index.mjs:475
react-stack-bottom-frame @ react-dom-client.development.js:24036
runWithFiberInDEV @ react-dom-client.development.js:1511
commitHookEffectListMount @ react-dom-client.development.js:10515
commitHookPassiveMountEffects @ react-dom-client.development.js:10636
commitPassiveMountOnFiber @ react-dom-client.development.js:12442
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12445
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12435
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12558
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:12454
flushPassiveEffects @ react-dom-client.development.js:15796
flushPendingEffects @ react-dom-client.development.js:15761
flushSpawnedWork @ react-dom-client.development.js:15656
commitRoot @ react-dom-client.development.js:15391
commitRootWhenReady @ react-dom-client.development.js:14644
performWorkOnRoot @ react-dom-client.development.js:14567
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<DescriptionWarning>
exports.jsx @ react-jsx-runtime.development.js:319
eval @ index.mjs:350
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooksAgain @ react-dom-client.development.js:5179
renderWithHooks @ react-dom-client.development.js:5091
updateForwardRef @ react-dom-client.development.js:8072
beginWork @ react-dom-client.development.js:10284
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<ForwardRef>
exports.jsx @ react-jsx-runtime.development.js:319
eval @ index.mjs:250
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooksAgain @ react-dom-client.development.js:5179
renderWithHooks @ react-dom-client.development.js:5091
updateForwardRef @ react-dom-client.development.js:8072
beginWork @ react-dom-client.development.js:10284
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<ForwardRef>
exports.jsx @ react-jsx-runtime.development.js:319
DialogContent @ index.mjs:218
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooksAgain @ react-dom-client.development.js:5179
renderWithHooks @ react-dom-client.development.js:5091
updateForwardRef @ react-dom-client.development.js:8072
beginWork @ react-dom-client.development.js:10284
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<DialogContent>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
_c1 @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\ui\dialog.tsx:38
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooksAgain @ react-dom-client.development.js:5179
renderWithHooks @ react-dom-client.development.js:5091
updateForwardRef @ react-dom-client.development.js:8072
beginWork @ react-dom-client.development.js:10284
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\ui\dialog.tsx:38 Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
overrideMethod @ hook.js:600
DescriptionWarning.useEffect @ index.mjs:475
react-stack-bottom-frame @ react-dom-client.development.js:24036
runWithFiberInDEV @ react-dom-client.development.js:1511
commitHookEffectListMount @ react-dom-client.development.js:10515
commitHookPassiveMountEffects @ react-dom-client.development.js:10636
reconnectPassiveEffects @ react-dom-client.development.js:12605
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:12577
reconnectPassiveEffects @ react-dom-client.development.js:12652
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:12577
reconnectPassiveEffects @ react-dom-client.development.js:12598
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:12577
reconnectPassiveEffects @ react-dom-client.development.js:12598
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:12577
reconnectPassiveEffects @ react-dom-client.development.js:12598
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:12577
reconnectPassiveEffects @ react-dom-client.development.js:12598
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:12577
reconnectPassiveEffects @ react-dom-client.development.js:12598
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:12577
reconnectPassiveEffects @ react-dom-client.development.js:12598
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:12577
reconnectPassiveEffects @ react-dom-client.development.js:12598
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:12577
reconnectPassiveEffects @ react-dom-client.development.js:12652
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16027
runWithFiberInDEV @ react-dom-client.development.js:1514
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15987
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15994
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16036
flushPassiveEffects @ react-dom-client.development.js:15806
flushPendingEffects @ react-dom-client.development.js:15761
flushSpawnedWork @ react-dom-client.development.js:15656
commitRoot @ react-dom-client.development.js:15391
commitRootWhenReady @ react-dom-client.development.js:14644
performWorkOnRoot @ react-dom-client.development.js:14567
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<DescriptionWarning>
exports.jsx @ react-jsx-runtime.development.js:319
eval @ index.mjs:350
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooksAgain @ react-dom-client.development.js:5179
renderWithHooks @ react-dom-client.development.js:5091
updateForwardRef @ react-dom-client.development.js:8072
beginWork @ react-dom-client.development.js:10284
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<ForwardRef>
exports.jsx @ react-jsx-runtime.development.js:319
eval @ index.mjs:250
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooksAgain @ react-dom-client.development.js:5179
renderWithHooks @ react-dom-client.development.js:5091
updateForwardRef @ react-dom-client.development.js:8072
beginWork @ react-dom-client.development.js:10284
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<ForwardRef>
exports.jsx @ react-jsx-runtime.development.js:319
DialogContent @ index.mjs:218
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooksAgain @ react-dom-client.development.js:5179
renderWithHooks @ react-dom-client.development.js:5091
updateForwardRef @ react-dom-client.development.js:8072
beginWork @ react-dom-client.development.js:10284
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309
<DialogContent>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:326
_c1 @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\ui\dialog.tsx:38
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooksAgain @ react-dom-client.development.js:5179
renderWithHooks @ react-dom-client.development.js:5091
updateForwardRef @ react-dom-client.development.js:8072
beginWork @ react-dom-client.development.js:10284
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_0
overrideMethod @ hook.js:608
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6171
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_1
overrideMethod @ hook.js:608
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6171
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_2
overrideMethod @ hook.js:608
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6171
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_0
overrideMethod @ hook.js:600
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6175
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_1
overrideMethod @ hook.js:600
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6175
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152 Exercise not found: ai_workout_1749850520601_2
overrideMethod @ hook.js:600
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:152
WorkoutProvider.useMemo[currentWorkoutMuscleVolumes] @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:148
updateMemo @ react-dom-client.development.js:6175
useMemo @ react-dom-client.development.js:23269
exports.useMemo @ react.development.js:1214
WorkoutProvider @ C:\Users\<USER>\Projects\Gymzy\Gymzy\src\contexts\WorkoutContext.tsx:145
react-stack-bottom-frame @ react-dom-client.development.js:23950
renderWithHooks @ react-dom-client.development.js:5079
updateFunctionComponent @ react-dom-client.development.js:8328
beginWork @ react-dom-client.development.js:9945
runWithFiberInDEV @ react-dom-client.development.js:1511
performUnitOfWork @ react-dom-client.development.js:15120
workLoopSync @ react-dom-client.development.js:14944
renderRootSync @ react-dom-client.development.js:14924
performWorkOnRoot @ react-dom-client.development.js:14411
performSyncWorkOnRoot @ react-dom-client.development.js:16290
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16138
processRootScheduleInMicrotask @ react-dom-client.development.js:16175
eval @ react-dom-client.development.js:16309Understand this warning
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\workout\page.tsx:54 WorkoutPage - currentWorkoutExercises length: 4, hasChanges: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\app\workout\page.tsx:54 WorkoutPage - currentWorkoutExercises length: 4, hasChanges: true
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\workout\muscle-activation-svg.tsx:178 MuscleActivationSVG - muscleVolumes prop: {Pectoralis Major: 0, Anterior Deltoid: 0, Lateral Deltoid: 0, Posterior Deltoid: 0, Deltoid: 0, …}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\workout\muscle-activation-svg.tsx:179 MuscleActivationSVG - safeMuscleVolumes: {Pectoralis Major: 0, Anterior Deltoid: 0, Lateral Deltoid: 0, Posterior Deltoid: 0, Deltoid: 0, …}
C:\Users\<USER>\Projects\Gymzy\Gymzy\src\components\workout\muscle-activation-svg.tsx:180 MuscleActivationSVG - relevantMuscles: []