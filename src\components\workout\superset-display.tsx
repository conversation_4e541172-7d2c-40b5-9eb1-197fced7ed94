"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExerciseWithSets } from '@/types/exercise';
import { Link, Timer, RotateCcw, Play, Pause, CheckCircle2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SupersetDisplayProps {
  exercises: ExerciseWithSets[];
  groupId: string;
  onSetExecuted: (exerciseIndex: number, setIndex: number) => void;
  isRestTimerRunning?: boolean;
  restTimeRemaining?: number;
  onToggleRestTimer?: () => void;
  className?: string;
}

export function SupersetDisplay({
  exercises,
  groupId,
  onSetExecuted,
  isRestTimerRunning = false,
  restTimeRemaining = 0,
  onToggleRestTimer,
  className
}: SupersetDisplayProps) {
  const supersetParameters = exercises[0]?.specialSetParameters;
  const totalRounds = supersetParameters?.rounds || 3;

  // Calculate current round more accurately
  const getCurrentRound = () => {
    for (let round = 0; round < totalRounds; round++) {
      const isRoundComplete = exercises.every(exercise =>
        exercise.sets[round]?.isExecuted || false
      );
      if (!isRoundComplete) {
        return round + 1;
      }
    }
    return totalRounds; // All rounds complete
  };

  const currentRound = getCurrentRound();

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const isRoundComplete = (roundIndex: number) => {
    return exercises.every(exercise => 
      exercise.sets[roundIndex]?.isExecuted || false
    );
  };

  const getNextExerciseInRound = (roundIndex: number) => {
    for (let i = 0; i < exercises.length; i++) {
      if (!exercises[i].sets[roundIndex]?.isExecuted) {
        return i;
      }
    }
    return -1;
  };

  const isSupersetComplete = () => {
    return exercises.every(exercise =>
      exercise.sets.every(set => set.isExecuted)
    );
  };

  const shouldShowRestTimer = () => {
    // Show rest timer if we just completed a round and there are more rounds to go
    const currentRoundIndex = currentRound - 1;
    if (currentRoundIndex >= 0 && currentRoundIndex < totalRounds - 1) {
      const isCurrentRoundComplete = exercises.every(exercise =>
        exercise.sets[currentRoundIndex]?.isExecuted || false
      );
      return isCurrentRoundComplete && !isSupersetComplete();
    }
    return false;
  };

  return (
    <Card className={cn("border-l-4 border-l-blue-500", className)}>
      <CardContent className="p-4">
        {/* Superset Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Link className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-lg">Superset</h3>
            <Badge variant="outline" className="text-xs">
              {exercises.length} exercises
            </Badge>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <RotateCcw className="h-4 w-4" />
            Round {currentRound}/{totalRounds}
          </div>
        </div>

        {/* Superset Status */}
        {isSupersetComplete() && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5 text-green-600" />
              <span className="font-medium text-green-900">Superset Complete!</span>
            </div>
          </div>
        )}

        {/* Rest Timer (if active) */}
        {(isRestTimerRunning && restTimeRemaining > 0) || shouldShowRestTimer() && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Timer className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-900">
                  {shouldShowRestTimer() ? 'Rest Between Superset Rounds' : 'Rest Between Sets'}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <span className="font-mono text-lg font-bold text-blue-600">
                  {formatTime(restTimeRemaining)}
                </span>
                {onToggleRestTimer && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onToggleRestTimer}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    {isRestTimerRunning ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Current Round Instructions */}
        {!isSupersetComplete() && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2">
              <RotateCcw className="h-4 w-4 text-yellow-600" />
              <span className="font-medium text-yellow-900">
                Round {currentRound} of {totalRounds} -
                {getNextExerciseInRound(currentRound - 1) !== -1
                  ? ` Next: ${exercises[getNextExerciseInRound(currentRound - 1)]?.name}`
                  : ' Round Complete - Rest before next round'
                }
              </span>
            </div>
          </div>
        )}

        {/* Exercise List */}
        <div className="space-y-3">
          {exercises.map((exercise, exerciseIndex) => {
            const nextExerciseIndex = getNextExerciseInRound(currentRound - 1);
            const isNextExercise = nextExerciseIndex === exerciseIndex;
            
            return (
              <div 
                key={exercise.id}
                className={cn(
                  "p-3 border rounded-lg transition-all",
                  isNextExercise ? "border-blue-300 bg-blue-50" : "border-gray-200"
                )}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={isNextExercise ? "default" : "outline"} 
                      className={cn(
                        "w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs",
                        isNextExercise ? "bg-blue-600" : ""
                      )}
                    >
                      {exerciseIndex + 1}
                    </Badge>
                    <h4 className="font-medium">{exercise.name}</h4>
                    {isNextExercise && (
                      <Badge variant="secondary" className="text-xs">
                        Next
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Sets for this exercise - showing as rounds */}
                <div className="grid grid-cols-1 gap-2">
                  {exercise.sets.map((set, setIndex) => {
                    const isCurrentRoundSet = setIndex === currentRound - 1;
                    const isAvailableToExecute = setIndex <= currentRound - 1;

                    return (
                      <div
                        key={setIndex}
                        className={cn(
                          "flex items-center justify-between p-2 rounded border transition-all",
                          isCurrentRoundSet ? "bg-blue-50 border-blue-200" : "bg-white border-gray-200",
                          !isAvailableToExecute && "opacity-50"
                        )}
                      >
                        <div className="flex items-center gap-4 text-sm">
                          <span className="w-16 text-gray-600">
                            Round {setIndex + 1}
                          </span>
                          <span>{set.weight}kg × {set.reps} reps</span>
                          {set.rpe && <span className="text-gray-500">RPE {set.rpe}</span>}
                          {isCurrentRoundSet && !set.isExecuted && (
                            <Badge variant="secondary" className="text-xs">
                              Current
                            </Badge>
                          )}
                        </div>

                        <Button
                          variant={set.isExecuted ? "default" : "outline"}
                          size="sm"
                          onClick={() => onSetExecuted(exerciseIndex, setIndex)}
                          disabled={!isAvailableToExecute}
                          className={cn(
                            "w-8 h-8 p-0",
                            set.isExecuted
                              ? "bg-green-600 hover:bg-green-700"
                              : isCurrentRoundSet
                              ? "border-blue-400 hover:bg-blue-50"
                              : "hover:bg-gray-50"
                          )}
                        >
                          {set.isExecuted ? "✓" : ""}
                        </Button>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>

        {/* Superset Instructions */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-sm mb-2">Superset Instructions:</h4>
          <ul className="text-xs text-gray-600 space-y-1">
            <li>• Perform exercises {exercises.map((_, i) => i + 1).join(' → ')} back-to-back</li>
            <li>• No rest between exercises in the superset</li>
            <li>• Rest {supersetParameters?.restBetweenSets || 90}s between superset rounds</li>
            <li>• Complete {totalRounds} total rounds</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
