/**
 * Caching system for special sets data
 * Implements intelligent caching with TTL and invalidation strategies
 */

import { SpecialSetResponse, SpecialSetTemplate } from '@/types/special-sets-unified';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  key: string;
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  size: number;
}

class SpecialSetsCache {
  private cache = new Map<string, CacheEntry<any>>();
  private stats: CacheStats = { hits: 0, misses: 0, evictions: 0, size: 0 };
  private maxSize = 100; // Maximum number of entries
  private defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL

  // Cache key generators
  private generateKey(prefix: string, ...parts: (string | number)[]): string {
    return `${prefix}:${parts.join(':')}`;
  }

  // Generic cache operations
  set<T>(key: string, data: T, ttl?: number): void {
    // Evict expired entries if cache is full
    if (this.cache.size >= this.maxSize) {
      this.evictExpired();
      
      // If still full, evict oldest entry
      if (this.cache.size >= this.maxSize) {
        const oldestKey = Array.from(this.cache.keys())[0];
        this.cache.delete(oldestKey);
        this.stats.evictions++;
      }
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
      key
    };

    this.cache.set(key, entry);
    this.stats.size = this.cache.size;
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      this.stats.evictions++;
      this.stats.size = this.cache.size;
      return null;
    }

    this.stats.hits++;
    return entry.data as T;
  }

  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.stats.size = this.cache.size;
    }
    return deleted;
  }

  clear(): void {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0, evictions: 0, size: 0 };
  }

  // Evict expired entries
  private evictExpired(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      this.cache.delete(key);
      this.stats.evictions++;
    });

    this.stats.size = this.cache.size;
  }

  // Special sets specific methods
  setSpecialSet(specialSet: SpecialSetResponse): void {
    const key = this.generateKey('special-set', specialSet.id);
    this.set(key, specialSet);
  }

  getSpecialSet(id: string): SpecialSetResponse | null {
    const key = this.generateKey('special-set', id);
    return this.get<SpecialSetResponse>(key);
  }

  setSpecialSets(workoutId: string, specialSets: SpecialSetResponse[]): void {
    const key = this.generateKey('special-sets', 'workout', workoutId);
    this.set(key, specialSets);
    
    // Also cache individual special sets
    specialSets.forEach(specialSet => {
      this.setSpecialSet(specialSet);
    });
  }

  getSpecialSets(workoutId: string): SpecialSetResponse[] | null {
    const key = this.generateKey('special-sets', 'workout', workoutId);
    return this.get<SpecialSetResponse[]>(key);
  }

  setTemplate(template: SpecialSetTemplate): void {
    const key = this.generateKey('template', template.id);
    this.set(key, template, 10 * 60 * 1000); // Templates cached for 10 minutes
  }

  getTemplate(id: string): SpecialSetTemplate | null {
    const key = this.generateKey('template', id);
    return this.get<SpecialSetTemplate>(key);
  }

  setTemplates(filters: Record<string, any>, templates: SpecialSetTemplate[]): void {
    const filterKey = Object.entries(filters)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    
    const key = this.generateKey('templates', filterKey);
    this.set(key, templates, 10 * 60 * 1000);
    
    // Also cache individual templates
    templates.forEach(template => {
      this.setTemplate(template);
    });
  }

  getTemplates(filters: Record<string, any>): SpecialSetTemplate[] | null {
    const filterKey = Object.entries(filters)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    
    const key = this.generateKey('templates', filterKey);
    return this.get<SpecialSetTemplate[]>(key);
  }

  // Cache invalidation methods
  invalidateSpecialSet(id: string): void {
    const key = this.generateKey('special-set', id);
    this.delete(key);
  }

  invalidateWorkoutSpecialSets(workoutId: string): void {
    const key = this.generateKey('special-sets', 'workout', workoutId);
    this.delete(key);
  }

  invalidateTemplate(id: string): void {
    const key = this.generateKey('template', id);
    this.delete(key);
  }

  invalidateAllTemplates(): void {
    const templateKeys = Array.from(this.cache.keys()).filter(key => 
      key.startsWith('template:') || key.startsWith('templates:')
    );
    
    templateKeys.forEach(key => this.delete(key));
  }

  // Cache statistics and management
  getStats(): CacheStats {
    return { ...this.stats };
  }

  getHitRate(): number {
    const total = this.stats.hits + this.stats.misses;
    return total > 0 ? this.stats.hits / total : 0;
  }

  // Configuration
  setMaxSize(size: number): void {
    this.maxSize = size;
    
    // Evict entries if current size exceeds new max
    while (this.cache.size > this.maxSize) {
      const oldestKey = Array.from(this.cache.keys())[0];
      this.cache.delete(oldestKey);
      this.stats.evictions++;
    }
    
    this.stats.size = this.cache.size;
  }

  setDefaultTTL(ttl: number): void {
    this.defaultTTL = ttl;
  }

  // Debugging and monitoring
  getCacheKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  getCacheSize(): number {
    return this.cache.size;
  }

  // Cleanup method for periodic maintenance
  cleanup(): void {
    this.evictExpired();
  }
}

// Singleton instance
export const specialSetsCache = new SpecialSetsCache();

// Auto-cleanup every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    specialSetsCache.cleanup();
  }, 5 * 60 * 1000);
}

// Cache-aware API wrapper
export class CachedSpecialSetsAPI {
  async getSpecialSet(id: string): Promise<SpecialSetResponse> {
    // Try cache first
    const cached = specialSetsCache.getSpecialSet(id);
    if (cached) {
      return cached;
    }

    // Fetch from API
    const response = await fetch(`/api/special-sets/${id}`);
    if (!response.ok) {
      throw new Error('Failed to fetch special set');
    }

    const specialSet = await response.json();
    
    // Cache the result
    specialSetsCache.setSpecialSet(specialSet);
    
    return specialSet;
  }

  async getSpecialSets(workoutId: string): Promise<SpecialSetResponse[]> {
    // Try cache first
    const cached = specialSetsCache.getSpecialSets(workoutId);
    if (cached) {
      return cached;
    }

    // Fetch from API
    const response = await fetch(`/api/special-sets?workoutId=${workoutId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch special sets');
    }

    const specialSets = await response.json();
    
    // Cache the result
    specialSetsCache.setSpecialSets(workoutId, specialSets);
    
    return specialSets;
  }

  async createSpecialSet(data: any): Promise<SpecialSetResponse> {
    const response = await fetch('/api/special-sets', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error('Failed to create special set');
    }

    const specialSet = await response.json();
    
    // Cache the new special set
    specialSetsCache.setSpecialSet(specialSet);
    
    // Invalidate workout cache
    if (data.workoutId) {
      specialSetsCache.invalidateWorkoutSpecialSets(data.workoutId);
    }
    
    return specialSet;
  }

  async updateSpecialSet(id: string, data: any): Promise<SpecialSetResponse> {
    const response = await fetch(`/api/special-sets/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error('Failed to update special set');
    }

    const specialSet = await response.json();
    
    // Update cache
    specialSetsCache.setSpecialSet(specialSet);
    
    // Invalidate workout cache
    if (specialSet.workoutId) {
      specialSetsCache.invalidateWorkoutSpecialSets(specialSet.workoutId);
    }
    
    return specialSet;
  }

  async deleteSpecialSet(id: string): Promise<void> {
    const response = await fetch(`/api/special-sets/${id}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error('Failed to delete special set');
    }

    // Remove from cache
    specialSetsCache.invalidateSpecialSet(id);
    
    // Note: We can't easily invalidate workout cache without knowing workoutId
    // Consider adding workoutId to delete response or maintaining reverse mapping
  }
}

export const cachedSpecialSetsAPI = new CachedSpecialSetsAPI();
