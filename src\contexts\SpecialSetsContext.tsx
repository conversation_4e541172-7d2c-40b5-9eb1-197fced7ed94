/**
 * Special Sets Context Provider
 * Eliminates prop drilling and provides centralized state management
 */

import React, { createContext, useContext, useCallback, useMemo } from 'react';
import { useOptimizedSpecialSets, UseOptimizedSpecialSetsOptions } from '@/hooks/useOptimizedSpecialSets';
import { 
  ExerciseWithSets, 
  SpecialSetType, 
  SpecialSetParameters,
  SpecialSetResponse,
  SpecialSetTemplate 
} from '@/types/special-sets-unified';

export interface SpecialSetsContextValue {
  // Data
  exercises: ExerciseWithSets[];
  specialSets: SpecialSetResponse[];
  templates: SpecialSetTemplate[];
  specialSetGroups: Map<string, {
    type: SpecialSetType;
    parameters: SpecialSetParameters;
    exerciseIds: string[];
    order: number[];
  }>;
  
  // State
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  errors: any[];
  pendingUpdatesCount: number;
  cacheStats: {
    hitRate: number;
    size: number;
  };
  
  // Actions
  createSpecialSet: (type: SpecialSetType, parameters: SpecialSetParameters, exerciseIds: string[]) => Promise<void>;
  updateSpecialSet: (id: string, updates: Partial<SpecialSetParameters>) => Promise<void>;
  deleteSpecialSet: (id: string) => Promise<void>;
  updateExercise: (exerciseId: string, field: string, value: any) => void;
  setExercises: (exercises: ExerciseWithSets[]) => void;
  loadTemplates: (filters?: Record<string, any>) => Promise<void>;
  refreshData: () => Promise<void>;
  flushPendingUpdates: () => void;
  clearCache: () => void;
  
  // Computed values
  standaloneExercises: ExerciseWithSets[];
  exercisesInSpecialSets: ExerciseWithSets[];
  specialSetGroupsArray: Array<{
    groupId: string;
    type: SpecialSetType;
    parameters: SpecialSetParameters;
    exercises: ExerciseWithSets[];
  }>;
  
  // Utility functions
  getExercise: (id: string) => ExerciseWithSets | undefined;
  getSpecialSetGroup: (groupId: string) => {
    type: SpecialSetType;
    parameters: SpecialSetParameters;
    exerciseIds: string[];
    order: number[];
  } | undefined;
  isExerciseInSpecialSet: (exerciseId: string) => boolean;
  getExerciseSpecialSetGroup: (exerciseId: string) => string | undefined;
}

const SpecialSetsContext = createContext<SpecialSetsContextValue | null>(null);

export interface SpecialSetsProviderProps {
  children: React.ReactNode;
  workoutId?: string;
  options?: UseOptimizedSpecialSetsOptions;
}

export function SpecialSetsProvider({ 
  children, 
  workoutId, 
  options = {} 
}: SpecialSetsProviderProps) {
  const optimizedSpecialSets = useOptimizedSpecialSets({
    workoutId,
    ...options
  });

  // Computed values
  const standaloneExercises = useMemo(() => {
    return optimizedSpecialSets.exercises.filter(exercise => !exercise.specialSetGroup);
  }, [optimizedSpecialSets.exercises]);

  const exercisesInSpecialSets = useMemo(() => {
    return optimizedSpecialSets.exercises.filter(exercise => exercise.specialSetGroup);
  }, [optimizedSpecialSets.exercises]);

  const specialSetGroupsArray = useMemo(() => {
    const groups: Array<{
      groupId: string;
      type: SpecialSetType;
      parameters: SpecialSetParameters;
      exercises: ExerciseWithSets[];
    }> = [];

    optimizedSpecialSets.specialSetGroups.forEach((group, groupId) => {
      const exercises = group.exerciseIds
        .map(id => optimizedSpecialSets.exercises.find(ex => ex.id === id))
        .filter(Boolean) as ExerciseWithSets[];

      groups.push({
        groupId,
        type: group.type,
        parameters: group.parameters,
        exercises
      });
    });

    return groups;
  }, [optimizedSpecialSets.specialSetGroups, optimizedSpecialSets.exercises]);

  // Utility functions
  const getExercise = useCallback((id: string) => {
    return optimizedSpecialSets.exercises.find(ex => ex.id === id);
  }, [optimizedSpecialSets.exercises]);

  const getSpecialSetGroup = useCallback((groupId: string) => {
    return optimizedSpecialSets.specialSetGroups.get(groupId);
  }, [optimizedSpecialSets.specialSetGroups]);

  const isExerciseInSpecialSet = useCallback((exerciseId: string) => {
    const exercise = getExercise(exerciseId);
    return !!exercise?.specialSetGroup;
  }, [getExercise]);

  const getExerciseSpecialSetGroup = useCallback((exerciseId: string) => {
    const exercise = getExercise(exerciseId);
    return exercise?.specialSetGroup;
  }, [getExercise]);

  const contextValue: SpecialSetsContextValue = {
    // Data from optimized hook
    ...optimizedSpecialSets,
    
    // Computed values
    standaloneExercises,
    exercisesInSpecialSets,
    specialSetGroupsArray,
    
    // Utility functions
    getExercise,
    getSpecialSetGroup,
    isExerciseInSpecialSet,
    getExerciseSpecialSetGroup
  };

  return (
    <SpecialSetsContext.Provider value={contextValue}>
      {children}
    </SpecialSetsContext.Provider>
  );
}

// Hook to use the context
export function useSpecialSetsContext(): SpecialSetsContextValue {
  const context = useContext(SpecialSetsContext);
  
  if (!context) {
    throw new Error('useSpecialSetsContext must be used within a SpecialSetsProvider');
  }
  
  return context;
}

// Specialized hooks for specific use cases
export function useSpecialSetExercises() {
  const { exercises, standaloneExercises, exercisesInSpecialSets } = useSpecialSetsContext();
  
  return {
    allExercises: exercises,
    standaloneExercises,
    exercisesInSpecialSets
  };
}

export function useSpecialSetGroups() {
  const { specialSetGroups, specialSetGroupsArray, getSpecialSetGroup } = useSpecialSetsContext();
  
  return {
    groups: specialSetGroups,
    groupsArray: specialSetGroupsArray,
    getGroup: getSpecialSetGroup
  };
}

export function useSpecialSetActions() {
  const {
    createSpecialSet,
    updateSpecialSet,
    deleteSpecialSet,
    updateExercise,
    setExercises,
    loadTemplates,
    refreshData,
    flushPendingUpdates,
    clearCache
  } = useSpecialSetsContext();
  
  return {
    createSpecialSet,
    updateSpecialSet,
    deleteSpecialSet,
    updateExercise,
    setExercises,
    loadTemplates,
    refreshData,
    flushPendingUpdates,
    clearCache
  };
}

export function useSpecialSetState() {
  const {
    isLoading,
    isCreating,
    isUpdating,
    errors,
    pendingUpdatesCount,
    cacheStats
  } = useSpecialSetsContext();
  
  return {
    isLoading,
    isCreating,
    isUpdating,
    errors,
    pendingUpdatesCount,
    cacheStats
  };
}

// Event system for component communication
export interface SpecialSetEvent {
  type: string;
  payload: any;
  timestamp: number;
  source?: string;
}

class SpecialSetEventBus {
  private listeners = new Map<string, Set<(event: SpecialSetEvent) => void>>();

  subscribe(eventType: string, callback: (event: SpecialSetEvent) => void): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    
    this.listeners.get(eventType)!.add(callback);
    
    return () => {
      this.listeners.get(eventType)?.delete(callback);
    };
  }

  emit(eventType: string, payload: any, source?: string): void {
    const event: SpecialSetEvent = {
      type: eventType,
      payload,
      timestamp: Date.now(),
      source
    };

    const listeners = this.listeners.get(eventType);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(event);
        } catch (error) {
          console.error(`Error in event listener for ${eventType}:`, error);
        }
      });
    }
  }

  clear(): void {
    this.listeners.clear();
  }
}

export const specialSetEventBus = new SpecialSetEventBus();

// Hook for event communication
export function useSpecialSetEvents() {
  const subscribe = useCallback((
    eventType: string, 
    callback: (event: SpecialSetEvent) => void
  ) => {
    return specialSetEventBus.subscribe(eventType, callback);
  }, []);

  const emit = useCallback((eventType: string, payload: any, source?: string) => {
    specialSetEventBus.emit(eventType, payload, source);
  }, []);

  return { subscribe, emit };
}

// Common event types
export const SPECIAL_SET_EVENTS = {
  EXERCISE_UPDATED: 'exercise:updated',
  SPECIAL_SET_CREATED: 'specialSet:created',
  SPECIAL_SET_UPDATED: 'specialSet:updated',
  SPECIAL_SET_DELETED: 'specialSet:deleted',
  TIMER_STARTED: 'timer:started',
  TIMER_PAUSED: 'timer:paused',
  TIMER_COMPLETED: 'timer:completed',
  VALIDATION_ERROR: 'validation:error',
  CACHE_INVALIDATED: 'cache:invalidated'
} as const;
