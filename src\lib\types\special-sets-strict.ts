/**
 * Strict TypeScript definitions for special sets
 * Ensures type safety and prevents runtime errors
 */

import { z } from 'zod';

// Zod schemas for runtime validation
export const ExerciseSetSchema = z.object({
  id: z.string().min(1),
  weight: z.number().min(0).optional(),
  reps: z.number().int().min(1).optional(),
  duration: z.number().min(0).optional(),
  distance: z.number().min(0).optional(),
  rpe: z.number().min(1).max(10).optional(),
  isExecuted: z.boolean().default(false),
  executedAt: z.date().optional(),
  notes: z.string().optional()
});

export const ExerciseWithSetsSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  primaryMuscleGroup: z.string().optional(),
  secondaryMuscleGroups: z.array(z.string()).default([]),
  equipment: z.string().optional(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).default('intermediate'),
  sets: z.array(ExerciseSetSchema).min(1),
  specialSetType: z.enum(['superset', 'circuit', 'dropset', 'restpause']).optional(),
  specialSetGroup: z.string().optional(),
  specialSetParameters: z.record(z.any()).optional(),
  order: z.number().int().min(0).optional()
});

export const SpecialSetParametersSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('superset'),
    rounds: z.number().int().min(1).max(10),
    restBetweenExercises: z.number().min(0).max(300),
    restBetweenSets: z.number().min(0).max(600),
    exercises: z.array(z.string()).min(2).max(4)
  }),
  z.object({
    type: z.literal('circuit'),
    workTime: z.number().min(10).max(300),
    restTime: z.number().min(0).max(120),
    rounds: z.number().int().min(1).max(10),
    restBetweenRounds: z.number().min(0).max(600),
    exercises: z.array(z.string()).min(3).max(8)
  }),
  z.object({
    type: z.literal('dropset'),
    dropType: z.enum(['single', 'double', 'triple']),
    dropPercentages: z.array(z.number().min(5).max(70)).min(1).max(3),
    restBetweenDrops: z.number().min(0).max(60),
    exerciseId: z.string().min(1)
  }),
  z.object({
    type: z.literal('restpause'),
    restPauseDuration: z.number().min(5).max(60),
    maxReps: z.number().int().min(1).max(50),
    targetReps: z.number().int().min(1).max(100),
    exerciseId: z.string().min(1)
  })
]);

export const SpecialSetResponseSchema = z.object({
  id: z.string().min(1),
  userId: z.string().min(1),
  workoutId: z.string().min(1),
  type: z.enum(['superset', 'circuit', 'dropset', 'restpause']),
  parameters: SpecialSetParametersSchema,
  exerciseIds: z.array(z.string()).min(1),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime()
});

export const SpecialSetTemplateSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1).max(100),
  description: z.string().max(500).default(''),
  type: z.enum(['superset', 'circuit', 'dropset', 'restpause']),
  parameters: SpecialSetParametersSchema,
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).default('intermediate'),
  estimatedDuration: z.number().min(0).max(7200), // Max 2 hours
  targetMuscleGroups: z.array(z.string()).default([]),
  createdBy: z.string().min(1),
  rating: z.number().min(0).max(5).default(0),
  usageCount: z.number().int().min(0).default(0),
  tags: z.array(z.string()).default([])
});

// Inferred types from schemas
export type ExerciseSet = z.infer<typeof ExerciseSetSchema>;
export type ExerciseWithSets = z.infer<typeof ExerciseWithSetsSchema>;
export type SpecialSetParameters = z.infer<typeof SpecialSetParametersSchema>;
export type SpecialSetResponse = z.infer<typeof SpecialSetResponseSchema>;
export type SpecialSetTemplate = z.infer<typeof SpecialSetTemplateSchema>;

// Utility types
export type SpecialSetType = SpecialSetParameters['type'];

export type SupersetParameters = Extract<SpecialSetParameters, { type: 'superset' }>;
export type CircuitParameters = Extract<SpecialSetParameters, { type: 'circuit' }>;
export type DropsetParameters = Extract<SpecialSetParameters, { type: 'dropset' }>;
export type RestPauseParameters = Extract<SpecialSetParameters, { type: 'restpause' }>;

// Validation functions
export function validateExerciseSet(data: unknown): ExerciseSet {
  return ExerciseSetSchema.parse(data);
}

export function validateExerciseWithSets(data: unknown): ExerciseWithSets {
  return ExerciseWithSetsSchema.parse(data);
}

export function validateSpecialSetParameters(data: unknown): SpecialSetParameters {
  return SpecialSetParametersSchema.parse(data);
}

export function validateSpecialSetResponse(data: unknown): SpecialSetResponse {
  return SpecialSetResponseSchema.parse(data);
}

export function validateSpecialSetTemplate(data: unknown): SpecialSetTemplate {
  return SpecialSetTemplateSchema.parse(data);
}

// Safe validation functions that return results
export function safeValidateExerciseSet(data: unknown): { success: true; data: ExerciseSet } | { success: false; error: z.ZodError } {
  const result = ExerciseSetSchema.safeParse(data);
  return result.success ? { success: true, data: result.data } : { success: false, error: result.error };
}

export function safeValidateSpecialSetParameters(data: unknown): { success: true; data: SpecialSetParameters } | { success: false; error: z.ZodError } {
  const result = SpecialSetParametersSchema.safeParse(data);
  return result.success ? { success: true, data: result.data } : { success: false, error: result.error };
}

// Type guards
export function isSuperset(params: SpecialSetParameters): params is SupersetParameters {
  return params.type === 'superset';
}

export function isCircuit(params: SpecialSetParameters): params is CircuitParameters {
  return params.type === 'circuit';
}

export function isDropset(params: SpecialSetParameters): params is DropsetParameters {
  return params.type === 'dropset';
}

export function isRestPause(params: SpecialSetParameters): params is RestPauseParameters {
  return params.type === 'restpause';
}

// Error types
export class SpecialSetValidationError extends Error {
  constructor(
    message: string,
    public readonly field: string,
    public readonly value: unknown,
    public readonly zodError?: z.ZodError
  ) {
    super(message);
    this.name = 'SpecialSetValidationError';
  }
}

export class SpecialSetTypeError extends Error {
  constructor(
    message: string,
    public readonly expectedType: string,
    public readonly actualType: string
  ) {
    super(message);
    this.name = 'SpecialSetTypeError';
  }
}

// Constants with strict typing
export const SPECIAL_SET_TYPES = ['superset', 'circuit', 'dropset', 'restpause'] as const;
export const DIFFICULTY_LEVELS = ['beginner', 'intermediate', 'advanced'] as const;
export const DROP_TYPES = ['single', 'double', 'triple'] as const;

// Validation constraints
export const VALIDATION_CONSTRAINTS = {
  superset: {
    rounds: { min: 1, max: 10 },
    restBetweenExercises: { min: 0, max: 300 },
    restBetweenSets: { min: 0, max: 600 },
    exercises: { min: 2, max: 4 }
  },
  circuit: {
    workTime: { min: 10, max: 300 },
    restTime: { min: 0, max: 120 },
    rounds: { min: 1, max: 10 },
    restBetweenRounds: { min: 0, max: 600 },
    exercises: { min: 3, max: 8 }
  },
  dropset: {
    dropPercentages: { min: 5, max: 70, count: { min: 1, max: 3 } },
    restBetweenDrops: { min: 0, max: 60 }
  },
  restpause: {
    restPauseDuration: { min: 5, max: 60 },
    maxReps: { min: 1, max: 50 },
    targetReps: { min: 1, max: 100 }
  }
} as const;

// Helper functions for type-safe operations
export function getValidationConstraints<T extends SpecialSetType>(type: T): typeof VALIDATION_CONSTRAINTS[T] {
  return VALIDATION_CONSTRAINTS[type];
}

export function createDefaultParameters(type: SpecialSetType): SpecialSetParameters {
  switch (type) {
    case 'superset':
      return {
        type: 'superset',
        rounds: 3,
        restBetweenExercises: 0,
        restBetweenSets: 90,
        exercises: []
      };
    case 'circuit':
      return {
        type: 'circuit',
        workTime: 45,
        restTime: 15,
        rounds: 3,
        restBetweenRounds: 120,
        exercises: []
      };
    case 'dropset':
      return {
        type: 'dropset',
        dropType: 'single',
        dropPercentages: [20],
        restBetweenDrops: 10,
        exerciseId: ''
      };
    case 'restpause':
      return {
        type: 'restpause',
        restPauseDuration: 15,
        maxReps: 5,
        targetReps: 20,
        exerciseId: ''
      };
    default:
      throw new SpecialSetTypeError(
        `Unknown special set type: ${type}`,
        'SpecialSetType',
        typeof type
      );
  }
}

// Runtime type checking utilities
export function assertSpecialSetType(value: unknown): asserts value is SpecialSetType {
  if (typeof value !== 'string' || !SPECIAL_SET_TYPES.includes(value as SpecialSetType)) {
    throw new SpecialSetTypeError(
      `Invalid special set type: ${value}`,
      'SpecialSetType',
      typeof value
    );
  }
}

export function assertExerciseWithSets(value: unknown): asserts value is ExerciseWithSets {
  try {
    validateExerciseWithSets(value);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new SpecialSetValidationError(
        'Invalid ExerciseWithSets object',
        'ExerciseWithSets',
        value,
        error
      );
    }
    throw error;
  }
}
