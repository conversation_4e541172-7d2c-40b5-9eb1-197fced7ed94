/**
 * Optimized data flow manager for exercises and special sets
 * Handles efficient state updates, batching, and synchronization
 */

import React from 'react';
import { ExerciseWithSets, SpecialSetType, SpecialSetParameters } from '@/types/special-sets-unified';

export interface ExerciseUpdate {
  exerciseId: string;
  field: string;
  value: any;
  timestamp: number;
}

export interface BatchUpdate {
  updates: ExerciseUpdate[];
  batchId: string;
  timestamp: number;
}

export interface ExerciseDataState {
  exercises: Map<string, ExerciseWithSets>;
  specialSetGroups: Map<string, {
    type: SpecialSetType;
    parameters: SpecialSetParameters;
    exerciseIds: string[];
    order: number[];
  }>;
  pendingUpdates: Map<string, ExerciseUpdate>;
  lastSyncTimestamp: number;
}

class ExerciseDataManager {
  private state: ExerciseDataState = {
    exercises: new Map(),
    specialSetGroups: new Map(),
    pendingUpdates: new Map(),
    lastSyncTimestamp: 0
  };

  private updateQueue: ExerciseUpdate[] = [];
  private batchTimeout: NodeJS.Timeout | null = null;
  private batchDelay = 100; // ms
  private subscribers = new Set<(state: ExerciseDataState) => void>();

  // State management
  getState(): ExerciseDataState {
    return {
      exercises: new Map(this.state.exercises),
      specialSetGroups: new Map(this.state.specialSetGroups),
      pendingUpdates: new Map(this.state.pendingUpdates),
      lastSyncTimestamp: this.state.lastSyncTimestamp
    };
  }

  subscribe(callback: (state: ExerciseDataState) => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  private notifySubscribers(): void {
    const state = this.getState();
    this.subscribers.forEach(callback => {
      try {
        callback(state);
      } catch (error) {
        console.error('Error in exercise data subscriber:', error);
      }
    });
  }

  // Exercise management
  setExercises(exercises: ExerciseWithSets[]): void {
    this.state.exercises.clear();
    
    exercises.forEach(exercise => {
      this.state.exercises.set(exercise.id, { ...exercise });
    });

    this.rebuildSpecialSetGroups();
    this.state.lastSyncTimestamp = Date.now();
    this.notifySubscribers();
  }

  getExercise(id: string): ExerciseWithSets | undefined {
    return this.state.exercises.get(id);
  }

  getExercises(): ExerciseWithSets[] {
    return Array.from(this.state.exercises.values());
  }

  getExercisesByIds(ids: string[]): ExerciseWithSets[] {
    return ids.map(id => this.state.exercises.get(id)).filter(Boolean) as ExerciseWithSets[];
  }

  // Optimized update methods
  updateExercise(exerciseId: string, field: string, value: any): void {
    const update: ExerciseUpdate = {
      exerciseId,
      field,
      value,
      timestamp: Date.now()
    };

    // Add to queue for batching
    this.updateQueue.push(update);
    this.state.pendingUpdates.set(`${exerciseId}:${field}`, update);

    // Schedule batch processing
    this.scheduleBatchUpdate();
  }

  private scheduleBatchUpdate(): void {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }

    this.batchTimeout = setTimeout(() => {
      this.processBatchUpdates();
    }, this.batchDelay);
  }

  private processBatchUpdates(): void {
    if (this.updateQueue.length === 0) return;

    const updates = [...this.updateQueue];
    this.updateQueue = [];

    // Group updates by exercise
    const updatesByExercise = new Map<string, ExerciseUpdate[]>();
    
    updates.forEach(update => {
      if (!updatesByExercise.has(update.exerciseId)) {
        updatesByExercise.set(update.exerciseId, []);
      }
      updatesByExercise.get(update.exerciseId)!.push(update);
    });

    // Apply updates
    let hasChanges = false;
    
    updatesByExercise.forEach((exerciseUpdates, exerciseId) => {
      const exercise = this.state.exercises.get(exerciseId);
      if (!exercise) return;

      let updatedExercise = { ...exercise };
      
      exerciseUpdates.forEach(update => {
        updatedExercise = this.applyUpdate(updatedExercise, update);
        this.state.pendingUpdates.delete(`${update.exerciseId}:${update.field}`);
      });

      this.state.exercises.set(exerciseId, updatedExercise);
      hasChanges = true;
    });

    if (hasChanges) {
      this.state.lastSyncTimestamp = Date.now();
      this.notifySubscribers();
    }
  }

  private applyUpdate(exercise: ExerciseWithSets, update: ExerciseUpdate): ExerciseWithSets {
    const { field, value } = update;
    
    // Handle nested field updates (e.g., "sets.0.weight")
    if (field.includes('.')) {
      const parts = field.split('.');
      const result = { ...exercise };
      
      let current: any = result;
      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        if (Array.isArray(current[part])) {
          current[part] = [...current[part]];
        } else if (typeof current[part] === 'object') {
          current[part] = { ...current[part] };
        }
        current = current[part];
      }
      
      current[parts[parts.length - 1]] = value;
      return result;
    }

    // Handle direct field updates
    return { ...exercise, [field]: value };
  }

  // Special set group management
  private rebuildSpecialSetGroups(): void {
    this.state.specialSetGroups.clear();
    
    const groupMap = new Map<string, {
      type: SpecialSetType;
      parameters: SpecialSetParameters;
      exerciseIds: string[];
      order: number[];
    }>();

    this.state.exercises.forEach(exercise => {
      if (exercise.specialSetGroup && exercise.specialSetType && exercise.specialSetParameters) {
        if (!groupMap.has(exercise.specialSetGroup)) {
          groupMap.set(exercise.specialSetGroup, {
            type: exercise.specialSetType,
            parameters: exercise.specialSetParameters,
            exerciseIds: [],
            order: []
          });
        }

        const group = groupMap.get(exercise.specialSetGroup)!;
        group.exerciseIds.push(exercise.id);
        group.order.push(exercise.order || 0);
      }
    });

    // Sort exercises within each group by order
    groupMap.forEach(group => {
      const combined = group.exerciseIds.map((id, index) => ({
        id,
        order: group.order[index]
      }));
      
      combined.sort((a, b) => a.order - b.order);
      
      group.exerciseIds = combined.map(item => item.id);
      group.order = combined.map(item => item.order);
    });

    this.state.specialSetGroups = groupMap;
  }

  getSpecialSetGroups(): Map<string, {
    type: SpecialSetType;
    parameters: SpecialSetParameters;
    exerciseIds: string[];
    order: number[];
  }> {
    return new Map(this.state.specialSetGroups);
  }

  getSpecialSetGroup(groupId: string) {
    return this.state.specialSetGroups.get(groupId);
  }

  createSpecialSetGroup(
    groupId: string,
    type: SpecialSetType,
    parameters: SpecialSetParameters,
    exerciseIds: string[]
  ): void {
    // Update exercises to belong to this group
    exerciseIds.forEach((exerciseId, index) => {
      const exercise = this.state.exercises.get(exerciseId);
      if (exercise) {
        const updatedExercise = {
          ...exercise,
          specialSetType: type,
          specialSetGroup: groupId,
          specialSetParameters: parameters,
          order: index
        };
        this.state.exercises.set(exerciseId, updatedExercise);
      }
    });

    // Rebuild groups
    this.rebuildSpecialSetGroups();
    this.state.lastSyncTimestamp = Date.now();
    this.notifySubscribers();
  }

  removeSpecialSetGroup(groupId: string): void {
    const group = this.state.specialSetGroups.get(groupId);
    if (!group) return;

    // Remove special set properties from exercises
    group.exerciseIds.forEach(exerciseId => {
      const exercise = this.state.exercises.get(exerciseId);
      if (exercise) {
        const { specialSetType, specialSetGroup, specialSetParameters, order, ...cleanExercise } = exercise;
        this.state.exercises.set(exerciseId, cleanExercise as ExerciseWithSets);
      }
    });

    // Rebuild groups
    this.rebuildSpecialSetGroups();
    this.state.lastSyncTimestamp = Date.now();
    this.notifySubscribers();
  }

  // Utility methods
  getStandaloneExercises(): ExerciseWithSets[] {
    return Array.from(this.state.exercises.values()).filter(
      exercise => !exercise.specialSetGroup
    );
  }

  getExercisesInSpecialSets(): ExerciseWithSets[] {
    return Array.from(this.state.exercises.values()).filter(
      exercise => exercise.specialSetGroup
    );
  }

  // Performance monitoring
  getPendingUpdatesCount(): number {
    return this.state.pendingUpdates.size;
  }

  getQueuedUpdatesCount(): number {
    return this.updateQueue.length;
  }

  // Force immediate processing (useful for testing or critical updates)
  flushUpdates(): void {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }
    this.processBatchUpdates();
  }

  // Cleanup
  destroy(): void {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }
    this.subscribers.clear();
    this.state.exercises.clear();
    this.state.specialSetGroups.clear();
    this.state.pendingUpdates.clear();
    this.updateQueue = [];
  }
}

// Singleton instance
export const exerciseDataManager = new ExerciseDataManager();

// React hook for using the exercise data manager
export function useExerciseData() {
  const [state, setState] = React.useState(() => exerciseDataManager.getState());

  React.useEffect(() => {
    const unsubscribe = exerciseDataManager.subscribe(setState);
    return unsubscribe;
  }, []);

  return {
    exercises: Array.from(state.exercises.values()),
    specialSetGroups: state.specialSetGroups,
    pendingUpdatesCount: state.pendingUpdates.size,
    lastSyncTimestamp: state.lastSyncTimestamp,
    
    // Actions
    updateExercise: exerciseDataManager.updateExercise.bind(exerciseDataManager),
    createSpecialSetGroup: exerciseDataManager.createSpecialSetGroup.bind(exerciseDataManager),
    removeSpecialSetGroup: exerciseDataManager.removeSpecialSetGroup.bind(exerciseDataManager),
    getExercise: exerciseDataManager.getExercise.bind(exerciseDataManager),
    getSpecialSetGroup: exerciseDataManager.getSpecialSetGroup.bind(exerciseDataManager),
    flushUpdates: exerciseDataManager.flushUpdates.bind(exerciseDataManager)
  };
}
