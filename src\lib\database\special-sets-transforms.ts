/**
 * Database transformation layer for special sets
 * Handles conversion between database snake_case and TypeScript camelCase
 */

import { 
  SpecialSetResponse, 
  SpecialSetTemplate,
  CreateSpecialSetRequest,
  UpdateSpecialSetRequest 
} from '@/types/special-sets-unified';

// Database row types (snake_case)
export interface SpecialSetRow {
  id: string;
  user_id: string;
  workout_id: string | null;
  type: string;
  name?: string;
  parameters: any;
  exercise_ids: string[];
  created_at: string;
  updated_at: string;
}

export interface SpecialSetTemplateRow {
  id: string;
  name: string;
  description: string | null;
  type: string;
  parameters: any;
  difficulty: string | null;
  estimated_duration: number | null;
  target_muscle_groups: string[] | null;
  created_by: string | null;
  rating: number;
  usage_count: number;
  tags: string[] | null;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export interface SpecialSetExecutionRow {
  id: string;
  special_set_id: string;
  exercise_id: string;
  round_number: number;
  set_number: number;
  execution_time: string;
  performance_data: any;
  completed: boolean;
  created_at: string;
}

// Transformation functions
export function transformSpecialSetFromDb(row: SpecialSetRow): SpecialSetResponse {
  return {
    id: row.id,
    userId: row.user_id,
    workoutId: row.workout_id || '',
    type: row.type as any,
    parameters: row.parameters,
    exerciseIds: row.exercise_ids,
    createdAt: row.created_at,
    updatedAt: row.updated_at,
  };
}

export function transformSpecialSetToDb(request: CreateSpecialSetRequest, userId: string): Omit<SpecialSetRow, 'id' | 'created_at' | 'updated_at'> {
  return {
    user_id: userId,
    workout_id: request.workoutId || null,
    type: request.type,
    parameters: request.parameters,
    exercise_ids: request.exerciseIds,
  };
}

export function transformSpecialSetUpdateToDb(request: UpdateSpecialSetRequest): Partial<Pick<SpecialSetRow, 'parameters' | 'exercise_ids'>> {
  const update: Partial<Pick<SpecialSetRow, 'parameters' | 'exercise_ids'>> = {};
  
  if (request.parameters !== undefined) {
    update.parameters = request.parameters;
  }
  
  if (request.exerciseIds !== undefined) {
    update.exercise_ids = request.exerciseIds;
  }
  
  return update;
}

export function transformTemplateFromDb(row: SpecialSetTemplateRow): SpecialSetTemplate {
  return {
    id: row.id,
    name: row.name,
    description: row.description || '',
    type: row.type as any,
    parameters: row.parameters,
    difficulty: (row.difficulty as any) || 'intermediate',
    estimatedDuration: row.estimated_duration || 0,
    targetMuscleGroups: row.target_muscle_groups || [],
    createdBy: row.created_by || '',
    rating: row.rating || 0,
    usageCount: row.usage_count || 0,
    tags: row.tags || [],
  };
}

export function transformTemplateToDb(template: Omit<SpecialSetTemplate, 'id' | 'rating' | 'usageCount'>, userId: string): Omit<SpecialSetTemplateRow, 'id' | 'rating' | 'usage_count' | 'created_at' | 'updated_at'> {
  return {
    name: template.name,
    description: template.description || null,
    type: template.type,
    parameters: template.parameters,
    difficulty: template.difficulty || null,
    estimated_duration: template.estimatedDuration || null,
    target_muscle_groups: template.targetMuscleGroups.length > 0 ? template.targetMuscleGroups : null,
    created_by: userId,
    tags: template.tags.length > 0 ? template.tags : null,
    is_public: false, // Default to private
  };
}

// Validation helpers
export function validateDatabaseRow(row: any): row is SpecialSetRow {
  return (
    typeof row.id === 'string' &&
    typeof row.user_id === 'string' &&
    (typeof row.workout_id === 'string' || row.workout_id === null) &&
    typeof row.type === 'string' &&
    typeof row.parameters === 'object' &&
    Array.isArray(row.exercise_ids) &&
    typeof row.created_at === 'string' &&
    typeof row.updated_at === 'string'
  );
}

export function validateTemplateRow(row: any): row is SpecialSetTemplateRow {
  return (
    typeof row.id === 'string' &&
    typeof row.name === 'string' &&
    typeof row.type === 'string' &&
    typeof row.parameters === 'object' &&
    typeof row.created_at === 'string' &&
    typeof row.updated_at === 'string'
  );
}

// Error handling
export class DatabaseTransformError extends Error {
  constructor(message: string, public readonly originalData?: any) {
    super(message);
    this.name = 'DatabaseTransformError';
  }
}

export function safeTransformSpecialSetFromDb(row: any): SpecialSetResponse {
  try {
    if (!validateDatabaseRow(row)) {
      throw new DatabaseTransformError('Invalid special set row structure', row);
    }
    return transformSpecialSetFromDb(row);
  } catch (error) {
    if (error instanceof DatabaseTransformError) {
      throw error;
    }
    throw new DatabaseTransformError(`Failed to transform special set: ${error}`, row);
  }
}

export function safeTransformTemplateFromDb(row: any): SpecialSetTemplate {
  try {
    if (!validateTemplateRow(row)) {
      throw new DatabaseTransformError('Invalid template row structure', row);
    }
    return transformTemplateFromDb(row);
  } catch (error) {
    if (error instanceof DatabaseTransformError) {
      throw error;
    }
    throw new DatabaseTransformError(`Failed to transform template: ${error}`, row);
  }
}

// Batch transformation helpers
export function transformSpecialSetsFromDb(rows: any[]): SpecialSetResponse[] {
  return rows.map(row => safeTransformSpecialSetFromDb(row));
}

export function transformTemplatesFromDb(rows: any[]): SpecialSetTemplate[] {
  return rows.map(row => safeTransformTemplateFromDb(row));
}

// Query helpers for consistent column selection
export const SPECIAL_SET_COLUMNS = [
  'id',
  'user_id',
  'workout_id', 
  'type',
  'name',
  'parameters',
  'exercise_ids',
  'created_at',
  'updated_at'
].join(', ');

export const TEMPLATE_COLUMNS = [
  'id',
  'name',
  'description',
  'type',
  'parameters',
  'difficulty',
  'estimated_duration',
  'target_muscle_groups',
  'created_by',
  'rating',
  'usage_count',
  'tags',
  'is_public',
  'created_at',
  'updated_at'
].join(', ');
