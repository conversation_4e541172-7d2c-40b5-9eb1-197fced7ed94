import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp
} from 'firebase/firestore';

export async function POST(request: NextRequest) {
  try {
    // For now, we'll skip authentication since Firebase Auth setup might be different
    // TODO: Implement proper Firebase Auth verification
    const userId = 'temp-user-id'; // This should come from Firebase Auth

    const body = await request.json();
    const { specialSetId, exerciseId, roundNumber, setNumber, performanceData } = body;

    // Validate required fields
    if (!specialSetId || !exerciseId || roundNumber === undefined || setNumber === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: specialSetId, exerciseId, roundNumber, setNumber' },
        { status: 400 }
      );
    }

    // Verify the special set belongs to the user
    const specialSetRef = doc(db, 'special_sets', specialSetId);
    const specialSetSnap = await getDoc(specialSetRef);

    if (!specialSetSnap.exists() || specialSetSnap.data()?.user_id !== userId) {
      return NextResponse.json(
        { error: 'Special set not found or unauthorized' },
        { status: 404 }
      );
    }

    // Insert execution record
    const executionsRef = collection(db, 'special_set_executions');
    const docRef = await addDoc(executionsRef, {
      special_set_id: specialSetId,
      exercise_id: exerciseId,
      round_number: roundNumber,
      set_number: setNumber,
      performance_data: performanceData || {},
      completed: true,
      created_at: serverTimestamp()
    });

    const execution = {
      id: docRef.id,
      special_set_id: specialSetId,
      exercise_id: exerciseId,
      round_number: roundNumber,
      set_number: setNumber,
      performance_data: performanceData || {},
      completed: true,
      created_at: new Date().toISOString()
    };

    // Transform to response format
    const response = {
      id: execution.id,
      specialSetId: execution.special_set_id,
      exerciseId: execution.exercise_id,
      roundNumber: execution.round_number,
      setNumber: execution.set_number,
      performanceData: execution.performance_data,
      completed: execution.completed,
      createdAt: execution.created_at
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error('Error in executions POST:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // For now, we'll skip authentication since Firebase Auth setup might be different
    // TODO: Implement proper Firebase Auth verification
    const userId = 'temp-user-id'; // This should come from Firebase Auth

    const { searchParams } = new URL(request.url);
    const specialSetId = searchParams.get('specialSetId');
    const exerciseId = searchParams.get('exerciseId');

    // Build Firestore query
    const executionsRef = collection(db, 'special_set_executions');
    let q = query(executionsRef, orderBy('created_at', 'desc'));

    // Filter by special set if provided
    if (specialSetId) {
      // Verify the special set belongs to the user
      const specialSetRef = doc(db, 'special_sets', specialSetId);
      const specialSetSnap = await getDoc(specialSetRef);

      if (!specialSetSnap.exists() || specialSetSnap.data()?.user_id !== userId) {
        return NextResponse.json(
          { error: 'Special set not found or unauthorized' },
          { status: 404 }
        );
      }

      q = query(
        executionsRef,
        where('special_set_id', '==', specialSetId),
        orderBy('created_at', 'desc')
      );
    }

    // Filter by exercise if provided
    if (exerciseId) {
      if (specialSetId) {
        q = query(
          executionsRef,
          where('special_set_id', '==', specialSetId),
          where('exercise_id', '==', exerciseId),
          orderBy('created_at', 'desc')
        );
      } else {
        q = query(
          executionsRef,
          where('exercise_id', '==', exerciseId),
          orderBy('created_at', 'desc')
        );
      }
    }

    // Execute query
    const querySnapshot = await getDocs(q);
    const executions = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Transform to response format
    const response = executions.map((execution: any) => ({
      id: execution.id,
      specialSetId: execution.special_set_id,
      exerciseId: execution.exercise_id,
      roundNumber: execution.round_number,
      setNumber: execution.set_number,
      performanceData: execution.performance_data || {},
      completed: execution.completed || false,
      createdAt: execution.created_at
    }));

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in executions GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // For now, we'll skip authentication since Firebase Auth setup might be different
    // TODO: Implement proper Firebase Auth verification
    const userId = 'temp-user-id'; // This should come from Firebase Auth

    const { searchParams } = new URL(request.url);
    const specialSetId = searchParams.get('specialSetId');

    if (!specialSetId) {
      return NextResponse.json(
        { error: 'Special set ID is required' },
        { status: 400 }
      );
    }

    // Verify the special set belongs to the user
    const specialSetRef = doc(db, 'special_sets', specialSetId);
    const specialSetSnap = await getDoc(specialSetRef);

    if (!specialSetSnap.exists() || specialSetSnap.data()?.user_id !== userId) {
      return NextResponse.json(
        { error: 'Special set not found or unauthorized' },
        { status: 404 }
      );
    }

    // Get all executions for this special set
    const executionsRef = collection(db, 'special_set_executions');
    const q = query(
      executionsRef,
      where('special_set_id', '==', specialSetId)
    );

    const querySnapshot = await getDocs(q);

    // Delete all executions
    const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
    await Promise.all(deletePromises);

    return NextResponse.json({
      success: true,
      deletedCount: querySnapshot.docs.length
    });
  } catch (error) {
    console.error('Error in executions DELETE:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
