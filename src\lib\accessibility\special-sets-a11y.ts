/**
 * Accessibility utilities and helpers for special sets components
 * Ensures WCAG 2.1 AA compliance and screen reader support
 */

import React from 'react';
import { SpecialSetType, SpecialSetParameters } from '@/types/special-sets-unified';

// ARIA labels and descriptions
export const ARIA_LABELS = {
  specialSetModal: 'Special set creation dialog',
  exerciseSelector: 'Exercise selection for special set',
  parameterConfiguration: 'Special set parameter configuration',
  timerControls: 'Special set timer controls',
  progressIndicator: 'Special set progress indicator',
  validationFeedback: 'Validation feedback messages',
  
  // Buttons
  createSpecialSet: 'Create special set',
  editSpecialSet: 'Edit special set',
  deleteSpecialSet: 'Delete special set',
  startTimer: 'Start timer',
  pauseTimer: 'Pause timer',
  resetTimer: 'Reset timer',
  skipTimer: 'Skip timer',
  
  // Form controls
  roundsInput: 'Number of rounds',
  restTimeInput: 'Rest time in seconds',
  workTimeInput: 'Work time in seconds',
  dropPercentageInput: 'Drop percentage',
  
  // Status indicators
  timerRunning: 'Timer is running',
  timerPaused: 'Timer is paused',
  timerCompleted: 'Timer completed',
  validationError: 'Validation error',
  validationWarning: 'Validation warning'
} as const;

export const ARIA_DESCRIPTIONS = {
  superset: 'A superset combines two or more exercises performed back-to-back with minimal rest',
  circuit: 'A circuit involves performing multiple exercises in sequence with timed work and rest periods',
  dropset: 'A dropset reduces weight after reaching failure to continue the exercise',
  restpause: 'Rest-pause involves brief rests during a set to perform additional repetitions',
  
  exerciseSelection: 'Select exercises to include in your special set. Use arrow keys to navigate and space to select.',
  parameterConfig: 'Configure the parameters for your special set. All fields are required.',
  timerDisplay: 'Current timer status and remaining time',
  progressDisplay: 'Shows your progress through the special set'
} as const;

// Screen reader announcements
export function announceSpecialSetCreated(type: SpecialSetType, exerciseCount: number): string {
  return `${type} created successfully with ${exerciseCount} exercise${exerciseCount !== 1 ? 's' : ''}`;
}

export function announceTimerUpdate(timeRemaining: number, phase: string): string {
  const minutes = Math.floor(timeRemaining / 60);
  const seconds = timeRemaining % 60;
  const timeString = minutes > 0 ? `${minutes} minute${minutes !== 1 ? 's' : ''} and ${seconds} second${seconds !== 1 ? 's' : ''}` : `${seconds} second${seconds !== 1 ? 's' : ''}`;
  return `${phase} phase: ${timeString} remaining`;
}

export function announceValidationError(field: string, message: string): string {
  return `Validation error in ${field}: ${message}`;
}

export function announceProgressUpdate(completed: number, total: number): string {
  return `Progress: ${completed} of ${total} sets completed`;
}

// Keyboard navigation helpers
export const KEYBOARD_SHORTCUTS = {
  createSpecialSet: 'c',
  startTimer: 'space',
  pauseTimer: 'space',
  resetTimer: 'r',
  skipTimer: 's',
  nextExercise: 'n',
  previousExercise: 'p',
  toggleValidation: 'v'
} as const;

export function handleKeyboardShortcut(
  event: KeyboardEvent,
  shortcuts: Record<string, () => void>
): boolean {
  const key = event.key.toLowerCase();
  const isModifierPressed = event.ctrlKey || event.metaKey || event.altKey;
  
  // Don't handle shortcuts when typing in inputs
  if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
    return false;
  }
  
  // Don't handle shortcuts with modifiers (except for specific cases)
  if (isModifierPressed) {
    return false;
  }
  
  const shortcutKey = Object.entries(KEYBOARD_SHORTCUTS).find(([, shortcut]) => shortcut === key)?.[0];
  
  if (shortcutKey && shortcuts[shortcutKey]) {
    event.preventDefault();
    shortcuts[shortcutKey]();
    return true;
  }
  
  return false;
}

// Focus management
export class FocusManager {
  private focusStack: HTMLElement[] = [];
  private trapElement: HTMLElement | null = null;
  
  pushFocus(element: HTMLElement): void {
    if (document.activeElement instanceof HTMLElement) {
      this.focusStack.push(document.activeElement);
    }
    element.focus();
  }
  
  popFocus(): void {
    const previousElement = this.focusStack.pop();
    if (previousElement) {
      previousElement.focus();
    }
  }
  
  trapFocus(container: HTMLElement): void {
    this.trapElement = container;
    container.addEventListener('keydown', this.handleFocusTrap);
  }
  
  releaseFocusTrap(): void {
    if (this.trapElement) {
      this.trapElement.removeEventListener('keydown', this.handleFocusTrap);
      this.trapElement = null;
    }
  }
  
  private handleFocusTrap = (event: KeyboardEvent): void => {
    if (event.key !== 'Tab' || !this.trapElement) return;
    
    const focusableElements = this.getFocusableElements(this.trapElement);
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement?.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement?.focus();
      }
    }
  };
  
  private getFocusableElements(container: HTMLElement): HTMLElement[] {
    const selector = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])'
    ].join(', ');
    
    return Array.from(container.querySelectorAll(selector)) as HTMLElement[];
  }
}

export const focusManager = new FocusManager();

// Color contrast utilities
export function getContrastRatio(color1: string, color2: string): number {
  const getLuminance = (color: string): number => {
    // Simplified luminance calculation
    const rgb = color.match(/\d+/g);
    if (!rgb) return 0;
    
    const [r, g, b] = rgb.map(c => {
      const val = parseInt(c) / 255;
      return val <= 0.03928 ? val / 12.92 : Math.pow((val + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  };
  
  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
}

export function meetsWCAGContrast(foreground: string, background: string, level: 'AA' | 'AAA' = 'AA'): boolean {
  const ratio = getContrastRatio(foreground, background);
  return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
}

// Screen reader utilities
export class ScreenReaderAnnouncer {
  private announcer: HTMLElement;
  
  constructor() {
    this.announcer = this.createAnnouncer();
  }
  
  private createAnnouncer(): HTMLElement {
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.style.position = 'absolute';
    announcer.style.left = '-10000px';
    announcer.style.width = '1px';
    announcer.style.height = '1px';
    announcer.style.overflow = 'hidden';
    document.body.appendChild(announcer);
    return announcer;
  }
  
  announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    this.announcer.setAttribute('aria-live', priority);
    this.announcer.textContent = message;
    
    // Clear after announcement
    setTimeout(() => {
      this.announcer.textContent = '';
    }, 1000);
  }
  
  destroy(): void {
    if (this.announcer.parentNode) {
      this.announcer.parentNode.removeChild(this.announcer);
    }
  }
}

export const screenReaderAnnouncer = new ScreenReaderAnnouncer();

// Reduced motion utilities
export function prefersReducedMotion(): boolean {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

export function getAnimationDuration(defaultDuration: number): number {
  return prefersReducedMotion() ? 0 : defaultDuration;
}

// High contrast mode detection
export function isHighContrastMode(): boolean {
  return window.matchMedia('(prefers-contrast: high)').matches;
}

// Touch target size validation
export function validateTouchTargetSize(element: HTMLElement): boolean {
  const rect = element.getBoundingClientRect();
  const minSize = 44; // WCAG minimum touch target size
  return rect.width >= minSize && rect.height >= minSize;
}

// Accessibility testing helpers
export function runA11yChecks(container: HTMLElement): string[] {
  const issues: string[] = [];
  
  // Check for missing alt text on images
  const images = container.querySelectorAll('img:not([alt])');
  if (images.length > 0) {
    issues.push(`${images.length} image(s) missing alt text`);
  }
  
  // Check for buttons without accessible names
  const buttons = container.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
  const buttonsWithoutText = Array.from(buttons).filter(btn => !btn.textContent?.trim());
  if (buttonsWithoutText.length > 0) {
    issues.push(`${buttonsWithoutText.length} button(s) without accessible names`);
  }
  
  // Check for form inputs without labels
  const inputs = container.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
  const inputsWithoutLabels = Array.from(inputs).filter(input => {
    const id = input.getAttribute('id');
    return !id || !container.querySelector(`label[for="${id}"]`);
  });
  if (inputsWithoutLabels.length > 0) {
    issues.push(`${inputsWithoutLabels.length} input(s) without labels`);
  }
  
  // Check touch target sizes
  const interactiveElements = container.querySelectorAll('button, a, input, select, textarea');
  const smallTargets = Array.from(interactiveElements).filter(el => 
    !validateTouchTargetSize(el as HTMLElement)
  );
  if (smallTargets.length > 0) {
    issues.push(`${smallTargets.length} interactive element(s) below minimum touch target size`);
  }
  
  return issues;
}

// React hook for accessibility
export function useAccessibility() {
  const announce = React.useCallback((message: string, priority?: 'polite' | 'assertive') => {
    screenReaderAnnouncer.announce(message, priority);
  }, []);
  
  const handleKeyboardShortcuts = React.useCallback((shortcuts: Record<string, () => void>) => {
    const handleKeyDown = (event: KeyboardEvent) => {
      handleKeyboardShortcut(event, shortcuts);
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);
  
  return {
    announce,
    handleKeyboardShortcuts,
    focusManager,
    prefersReducedMotion: prefersReducedMotion(),
    isHighContrast: isHighContrastMode()
  };
}
