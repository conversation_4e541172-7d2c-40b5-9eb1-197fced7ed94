name: 🔄 Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  CACHE_KEY: node-modules

jobs:
  # Quality checks job
  quality:
    name: 🔍 Code Quality
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔍 TypeScript check
        run: npm run typecheck

      - name: 🧹 Lint check
        run: npm run lint

      - name: 📊 Check formatting
        run: npx prettier --check .

  # Testing job
  test:
    name: 🧪 Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🧪 Run tests
        run: npm run test:ci
        env:
          NODE_ENV: test

      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # Build job
  build:
    name: 🏗️ Build
    runs-on: ubuntu-latest
    needs: [quality, test]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🏗️ Build application
        run: npm run build
        env:
          NEXT_PUBLIC_GOOGLE_AI_API_KEY: ${{ secrets.GOOGLE_AI_API_KEY }}
          GROQ_API_KEY: ${{ secrets.GROQ_API_KEY }}
          NEXT_PUBLIC_FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
          NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
          NEXT_PUBLIC_FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
          NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET }}
          NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID }}
          NEXT_PUBLIC_FIREBASE_APP_ID: ${{ secrets.FIREBASE_APP_ID }}
          NEXT_PUBLIC_APP_URL: https://gymzy.vercel.app
          NEXT_PUBLIC_API_URL: https://gymzy.vercel.app/api

      - name: 📦 Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: .next/
          retention-days: 1

  # Security scan job
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔒 Run security audit
        run: npm audit --audit-level=high

      - name: 🔍 Run CodeQL analysis
        uses: github/codeql-action/init@v2
        with:
          languages: javascript

      - name: 🔍 Perform CodeQL analysis
        uses: github/codeql-action/analyze@v2

  # Dependency check job
  dependencies:
    name: 📦 Dependencies
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Check for outdated dependencies
        run: npm outdated || true

      - name: 🔍 Check for unused dependencies
        run: npx depcheck || true

  # Performance check job (only on main branch)
  performance:
    name: ⚡ Performance
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    needs: [build]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 📦 Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-files
          path: .next/

      - name: ⚡ Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # Notify on completion
  notify:
    name: 📢 Notify
    runs-on: ubuntu-latest
    needs: [quality, test, build, security]
    if: always()
    
    steps:
      - name: 📢 Notify success
        if: ${{ needs.quality.result == 'success' && needs.test.result == 'success' && needs.build.result == 'success' && needs.security.result == 'success' }}
        run: echo "✅ All CI checks passed!"

      - name: 📢 Notify failure
        if: ${{ needs.quality.result == 'failure' || needs.test.result == 'failure' || needs.build.result == 'failure' || needs.security.result == 'failure' }}
        run: echo "❌ Some CI checks failed!"
