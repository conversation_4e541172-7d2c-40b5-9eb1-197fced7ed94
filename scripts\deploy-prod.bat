@echo off
REM Production deployment script for Gymzy (Windows)
REM This script builds and deploys the app to Vercel with production settings

echo 🚀 Starting production deployment for Gymzy...

REM Check if we're in the right directory
if not exist "package.json" (
    echo ❌ Error: package.json not found. Please run this script from the project root.
    exit /b 1
)

REM Check if Vercel CLI is installed
where vercel >nul 2>nul
if %errorlevel% neq 0 (
    echo 📦 Installing Vercel CLI...
    npm install -g vercel@latest
)

REM Check environment variables
echo 🔍 Checking environment variables...

set missing_vars=0

if "%NEXT_PUBLIC_FIREBASE_API_KEY%"=="" (
    echo ❌ Missing NEXT_PUBLIC_FIREBASE_API_KEY
    set missing_vars=1
)

if "%NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN%"=="" (
    echo ❌ Missing NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
    set missing_vars=1
)

if "%NEXT_PUBLIC_FIREBASE_PROJECT_ID%"=="" (
    echo ❌ Missing NEXT_PUBLIC_FIREBASE_PROJECT_ID
    set missing_vars=1
)

if %missing_vars%==1 (
    echo.
    echo Please set these variables in your Vercel project settings or .env.local file.
    echo You can set them using: vercel env add ^<variable_name^>
    exit /b 1
)

echo ✅ Environment variables check passed

REM Clean previous builds
echo 🧹 Cleaning previous builds...
if exist ".next" rmdir /s /q ".next"
if exist ".vercel" rmdir /s /q ".vercel"

REM Install dependencies
echo 📦 Installing dependencies...
npm ci

REM Run type checking
echo 🔍 Running type checking...
npx tsc --noEmit --skipLibCheck

REM Run linting
echo 🔍 Running linting...
npm run lint

REM Build the project
echo 🏗️ Building project...
npm run build

REM Deploy to Vercel
echo 🚀 Deploying to Vercel...
vercel --prod

echo ✅ Production deployment completed successfully!
echo.
echo 🌐 Your app should now be available at your Vercel domain
echo 📊 Check the deployment status in your Vercel dashboard

pause
