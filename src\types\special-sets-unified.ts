// Unified Special Sets Type System
// This file consolidates all special sets related types to resolve conflicts

import { Muscle } from '@/lib/constants';

// ===== CORE EXERCISE TYPES =====

export interface Exercise {
  id: string;
  name: string;
  primaryMuscles: Muscle[];
  secondaryMuscles: Muscle[];
  category?: string;
  equipment?: string;
  instructions?: string[];
  tips?: string[];
  imageUrl?: string;
  videoUrl?: string;
}

export interface ExerciseSet {
  weight: number;
  reps: number;
  rpe?: number;
  isWarmup?: boolean;
  isExecuted?: boolean;
  // Additional properties for compatibility
  id?: string;
  duration?: number;
  distance?: number;
  targetReps?: number;
  targetWeight?: number;
  actualReps?: number;
  actualWeight?: number;
  actualDuration?: number;
  completed?: boolean;
  restTime?: number;
}

export interface ExerciseWithSets extends Exercise {
  sets: ExerciseSet[];
  notes?: string;
  order?: number;
  // Special sets properties
  specialSetType?: SpecialSetType;
  specialSetGroup?: string;
  specialSetParameters?: SpecialSetParameters;
  // Workout context properties
  restTime?: number;
  personalBest?: any;
  lastPerformed?: Date;
  averageRating?: number;
}

// ===== SPECIAL SETS TYPES =====

export type SpecialSetType = 'superset' | 'circuit' | 'dropset' | 'restpause';

// Base interface for all special set parameters
export interface BaseSpecialSetParameters {
  type: SpecialSetType;
  name?: string;
  description?: string;
  createdAt?: Date;
  updatedAt?: Date;
  workoutId?: string;
}

// Superset specific parameters
export interface SupersetParameters extends BaseSpecialSetParameters {
  type: 'superset';
  rounds: number; // 1-10 rounds
  restBetweenExercises: number; // 0-30 seconds
  restBetweenSets: number; // 30-300 seconds
  exercises: string[]; // 2-4 exercise IDs
}

// Circuit specific parameters
export interface CircuitParameters extends BaseSpecialSetParameters {
  type: 'circuit';
  workTime: number; // 15-120 seconds
  restTime: number; // 5-60 seconds
  rounds: number; // 1-10 rounds
  restBetweenRounds: number; // 30-300 seconds
  exercises: string[]; // 3-8 exercise IDs
  exerciseOrder?: 'sequential' | 'random';
}

// Drop set specific parameters
export interface DropSetParameters extends BaseSpecialSetParameters {
  type: 'dropset';
  dropType: 'single' | 'double' | 'triple';
  dropPercentages: number[]; // [20] or [20, 30] or [20, 30, 40]
  restBetweenDrops: number; // 0-30 seconds
  targetReps: number; // 3-20 reps per drop
  exerciseId: string; // Single exercise ID
}

// Rest-pause specific parameters
export interface RestPauseParameters extends BaseSpecialSetParameters {
  type: 'restpause';
  restPauseDuration: number; // 10-30 seconds
  miniSets: number; // 2-5 mini-sets
  targetTotalReps: number; // 15-50 total reps
  exerciseId: string; // Single exercise ID
}

// Union type for all special set parameters
export type SpecialSetParameters = 
  | SupersetParameters 
  | CircuitParameters 
  | DropSetParameters 
  | RestPauseParameters;

// ===== EXECUTION & STATE TYPES =====

export interface SpecialSetExecution {
  id: string;
  specialSetId: string;
  type: SpecialSetType;
  status: 'pending' | 'active' | 'resting' | 'completed' | 'paused';
  currentRound: number;
  currentExercise: number;
  currentPhase: 'prep' | 'work' | 'rest' | 'transition' | 'complete';
  timeRemaining: number;
  startedAt?: Date;
  completedAt?: Date;
  progress: ExecutionProgress;
}

export interface ExecutionProgress {
  totalRounds: number;
  completedRounds: number;
  totalExercises: number;
  completedExercises: number;
  totalSets: number;
  completedSets: number;
  estimatedTimeRemaining: number;
  actualDuration: number;
}

export interface TimerState {
  isRunning: boolean;
  timeRemaining: number;
  totalTime: number;
  phase: 'work' | 'rest' | 'transition';
  autoStart: boolean;
}

// ===== API TYPES =====

export interface CreateSpecialSetRequest {
  workoutId: string;
  type: SpecialSetType;
  parameters: SpecialSetParameters;
  exerciseIds: string[];
}

export interface UpdateSpecialSetRequest {
  parameters?: Partial<SpecialSetParameters>;
  exerciseIds?: string[];
}

export interface SpecialSetResponse {
  id: string;
  workoutId: string;
  userId: string;
  type: SpecialSetType;
  parameters: SpecialSetParameters;
  exerciseIds: string[];
  createdAt: string;
  updatedAt: string;
}

export interface SpecialSetError {
  code: string;
  message: string;
  field?: string;
  details?: Record<string, any>;
}

// ===== CONFIGURATION TYPES =====

export interface SpecialSetConfig {
  type: SpecialSetType;
  name: string;
  description: string;
  icon: string;
  color: {
    primary: string;
    secondary: string;
    accent: string;
  };
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: {
    min: number;
    max: number;
  };
  requirements: string[];
}

// ===== HOOK TYPES =====

export interface UseSpecialSetTimer {
  timeRemaining: number;
  isRunning: boolean;
  phase: 'work' | 'rest' | 'transition';
  totalTime: number;
  progress: number;
  phaseColor: string;
  phaseLabel: string;
  formattedTime: string;
  start: () => void;
  pause: () => void;
  resume: () => void;
  reset: () => void;
  skip: () => void;
}

export interface UseSpecialSetExecution {
  execution: SpecialSetExecution | null;
  progress: ExecutionProgress;
  start: (specialSetId: string) => void;
  complete: () => void;
  pause: () => void;
  resume: () => void;
  updateProgress: (progress: Partial<ExecutionProgress>) => void;
}

export interface UseSpecialSetValidation {
  validate: (parameters: SpecialSetParameters) => SpecialSetError[];
  isValid: boolean;
  errors: SpecialSetError[];
}

// ===== TEMPLATE TYPES =====

export interface SpecialSetTemplate {
  id: string;
  name: string;
  description: string;
  type: SpecialSetType;
  parameters: Omit<SpecialSetParameters, 'type'>;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number; // in minutes
  targetMuscleGroups: string[];
  createdBy: string;
  rating: number;
  usageCount: number;
  tags: string[];
}

// ===== VALIDATION TYPES =====

export interface ValidationRule {
  min: number;
  max: number;
}

export interface SpecialSetValidationRules {
  superset: {
    rounds: ValidationRule;
    restBetweenExercises: ValidationRule;
    restBetweenSets: ValidationRule;
    exercises: ValidationRule;
  };
  circuit: {
    workTime: ValidationRule;
    restTime: ValidationRule;
    rounds: ValidationRule;
    restBetweenRounds: ValidationRule;
    exercises: ValidationRule;
  };
  dropset: {
    dropPercentages: ValidationRule;
    restBetweenDrops: ValidationRule;
    targetReps: ValidationRule;
  };
  restpause: {
    restPauseDuration: ValidationRule;
    miniSets: ValidationRule;
    targetTotalReps: ValidationRule;
  };
}

// ===== CONSTANTS =====

export const SPECIAL_SET_CONFIGS: Record<SpecialSetType, SpecialSetConfig> = {
  superset: {
    type: 'superset',
    name: 'Superset',
    description: 'Combine 2-4 exercises performed back-to-back with no rest',
    icon: 'Link',
    color: {
      primary: '#3B82F6',
      secondary: '#DBEAFE',
      accent: '#1D4ED8'
    },
    difficulty: 'intermediate',
    estimatedTime: { min: 10, max: 30 },
    requirements: ['2-4 exercises', 'Similar muscle groups recommended']
  },
  circuit: {
    type: 'circuit',
    name: 'Circuit',
    description: 'Timed rounds of multiple exercises with work/rest intervals',
    icon: 'RotateCcw',
    color: {
      primary: '#10B981',
      secondary: '#D1FAE5',
      accent: '#047857'
    },
    difficulty: 'intermediate',
    estimatedTime: { min: 15, max: 45 },
    requirements: ['3-8 exercises', 'Timer recommended']
  },
  dropset: {
    type: 'dropset',
    name: 'Drop Set',
    description: 'Reduce weight and continue for additional reps to failure',
    icon: 'TrendingDown',
    color: {
      primary: '#F59E0B',
      secondary: '#FEF3C7',
      accent: '#D97706'
    },
    difficulty: 'advanced',
    estimatedTime: { min: 5, max: 15 },
    requirements: ['1 exercise', 'Adjustable weights']
  },
  restpause: {
    type: 'restpause',
    name: 'Rest-Pause',
    description: 'Short rest periods between mini-sets to extend the set',
    icon: 'Clock',
    color: {
      primary: '#8B5CF6',
      secondary: '#EDE9FE',
      accent: '#7C3AED'
    },
    difficulty: 'advanced',
    estimatedTime: { min: 5, max: 12 },
    requirements: ['1 exercise', 'High intensity']
  }
};

export const SPECIAL_SET_VALIDATION_RULES: SpecialSetValidationRules = {
  superset: {
    rounds: { min: 1, max: 10 },
    restBetweenExercises: { min: 0, max: 30 },
    restBetweenSets: { min: 30, max: 300 },
    exercises: { min: 2, max: 4 }
  },
  circuit: {
    workTime: { min: 15, max: 120 },
    restTime: { min: 5, max: 60 },
    rounds: { min: 1, max: 10 },
    restBetweenRounds: { min: 30, max: 300 },
    exercises: { min: 3, max: 8 }
  },
  dropset: {
    dropPercentages: { min: 10, max: 50 },
    restBetweenDrops: { min: 0, max: 30 },
    targetReps: { min: 3, max: 20 }
  },
  restpause: {
    restPauseDuration: { min: 10, max: 30 },
    miniSets: { min: 2, max: 5 },
    targetTotalReps: { min: 15, max: 50 }
  }
};

// ===== TYPE GUARDS =====

export const isSuperset = (params: SpecialSetParameters): params is SupersetParameters => {
  return params.type === 'superset';
};

export const isCircuit = (params: SpecialSetParameters): params is CircuitParameters => {
  return params.type === 'circuit';
};

export const isDropSet = (params: SpecialSetParameters): params is DropSetParameters => {
  return params.type === 'dropset';
};

export const isRestPause = (params: SpecialSetParameters): params is RestPauseParameters => {
  return params.type === 'restpause';
};

// ===== UTILITY TYPES =====

export type SpecialSetComponentProps = {
  exercises: ExerciseWithSets[];
  onSetExecuted: (exerciseIndex: number, setIndex: number) => void;
  onSetUpdated?: (exerciseIndex: number, setIndex: number, field: keyof ExerciseSet, value: number | boolean | undefined) => void;
  className?: string;
};

export type SpecialSetModalProps = {
  exercises: ExerciseWithSets[];
  isOpen: boolean;
  onClose: () => void;
  onCreateSpecialSet: (type: SpecialSetType, parameters: SpecialSetParameters, exerciseIds: string[]) => void;
  userLevel?: 'beginner' | 'intermediate' | 'advanced';
  workoutGoal?: 'strength' | 'hypertrophy' | 'endurance' | 'fat-loss';
  timeAvailable?: number;
};

// ===== HELPER FUNCTIONS =====

// Exercise grouping and ordering helpers
export function groupExercisesBySpecialSet(exercises: ExerciseWithSets[]): {
  groups: { [groupId: string]: ExerciseWithSets[] };
  standaloneExercises: ExerciseWithSets[];
} {
  const groups: { [groupId: string]: ExerciseWithSets[] } = {};
  const standaloneExercises: ExerciseWithSets[] = [];

  exercises.forEach(exercise => {
    if (exercise.specialSetGroup) {
      if (!groups[exercise.specialSetGroup]) {
        groups[exercise.specialSetGroup] = [];
      }
      groups[exercise.specialSetGroup].push(exercise);
    } else {
      standaloneExercises.push(exercise);
    }
  });

  // Sort exercises within each group by order
  Object.keys(groups).forEach(groupId => {
    groups[groupId].sort((a, b) => (a.order || 0) - (b.order || 0));
  });

  return { groups, standaloneExercises };
}

export function validateExerciseSelection(
  exercises: ExerciseWithSets[],
  type: SpecialSetType
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const rules = SPECIAL_SET_VALIDATION_RULES[type];

  if (!rules) {
    errors.push(`Unknown special set type: ${type}`);
    return { isValid: false, errors };
  }

  // Check exercise count
  if ('exercises' in rules) {
    const { min, max } = rules.exercises;
    if (exercises.length < min) {
      errors.push(`${type} requires at least ${min} exercises`);
    }
    if (exercises.length > max) {
      errors.push(`${type} supports maximum ${max} exercises`);
    }
  }

  // Check if exercises have proper sets
  const exercisesWithoutSets = exercises.filter(ex => !ex.sets || ex.sets.length === 0);
  if (exercisesWithoutSets.length > 0) {
    errors.push('All exercises must have at least one set');
  }

  // Type-specific validations
  if (type === 'dropset' && exercises.length > 1) {
    errors.push('Drop sets can only be applied to a single exercise');
  }

  if (type === 'restpause' && exercises.length > 1) {
    errors.push('Rest-pause sets can only be applied to a single exercise');
  }

  return { isValid: errors.length === 0, errors };
}

export function assignExercisesToSpecialSet(
  exercises: ExerciseWithSets[],
  exerciseIds: string[],
  type: SpecialSetType,
  parameters: SpecialSetParameters
): ExerciseWithSets[] {
  const groupId = `${type}_${Date.now()}`;

  return exercises.map(exercise => {
    if (exerciseIds.includes(exercise.id)) {
      return {
        ...exercise,
        specialSetType: type,
        specialSetGroup: groupId,
        specialSetParameters: parameters,
        order: exerciseIds.indexOf(exercise.id)
      };
    }
    return exercise;
  });
}

export function removeExerciseFromSpecialSet(
  exercises: ExerciseWithSets[],
  exerciseId: string
): ExerciseWithSets[] {
  return exercises.map(exercise => {
    if (exercise.id === exerciseId) {
      const { specialSetType, specialSetGroup, specialSetParameters, order, ...cleanExercise } = exercise;
      return cleanExercise as ExerciseWithSets;
    }
    return exercise;
  });
}
